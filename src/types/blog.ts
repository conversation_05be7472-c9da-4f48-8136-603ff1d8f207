// 部落格文章資料類型定義，對應 Google Sheets 欄位結構

export interface BlogData {
  // Google Sheets 原始欄位
  title: string;           // Title (標題)
  body: string;            // Body (內容，HTML 格式)
  hero: string;            // Hero (主圖片，HTML 格式)
  author: string;          // Author (作者，HTML 格式)
  thumbnail: string;       // Thumbnail (縮圖 URL)
  time: string;            // Time (發布時間)
  // SEO 欄位
  seoSlug: string;         // SEO:Slug (網址路徑)
  seoTitle: string;        // SEO:Title (meta 標題)
  seoDescription: string;  // SEO:Description (meta 描述)
  // 社群分享欄位
  socialImage: string;     // Social:Image (社群分享縮圖)
  socialTitle: string;     // Social:Title (社群分享標題)
  socialDescription: string; // Social:Description (社群分享描述)
}

// 前端使用的部落格文章類型（處理過的資料）
export interface BlogPost {
  slug: string;            // 網址路徑（來自 seoSlug 或自動生成）
  title: string;           // 文章標題
  body: string;            // 文章內容（HTML 格式）
  hero: string;            // 主圖片（HTML 格式）
  author: string;          // 作者（HTML 格式）
  thumbnail: string;       // 縮圖 URL
  publishDate: Date;       // 發布時間
  // SEO 欄位
  seoSlug: string;         // SEO:Slug (網址路徑)
  seoTitle: string;        // SEO:Title (meta 標題)
  seoDescription: string;  // SEO:Description (meta 描述)
  // 社群分享欄位
  socialImage: string;     // Social:Image (社群分享縮圖)
  socialTitle: string;     // Social:Title (社群分享標題)
  socialDescription: string; // Social:Description (社群分享描述)
}

// 列表頁面使用的簡化部落格文章類型
export interface BlogListItem {
  slug: string;            // 網址路徑
  title: string;           // 文章標題
  thumbnail: string;       // 縮圖 URL
  publishDate: Date;       // 發布時間
  author: string;          // 作者（HTML 格式，需要處理）
  seoDescription: string;  // 用作摘要顯示
}

// API 響應類型
export interface BlogResponse {
  posts: BlogListItem[];
  total: number;
  hasMore: boolean;
  page?: number;
  pageSize?: number;
  message?: string;
}

// Google Sheets 欄位對應
export const BLOG_SHEET_COLUMNS = {
  TITLE: 'A',              // Title
  BODY: 'B',               // Body
  HERO: 'C',               // Hero
  AUTHOR: 'D',             // Author
  THUMBNAIL: 'E',          // Thumbnail
  TIME: 'F',               // Time
  SEO_SLUG: 'G',           // SEO:Slug
  SEO_TITLE: 'H',          // SEO:Title
  SEO_DESCRIPTION: 'I',    // SEO:Description
  SOCIAL_IMAGE: 'J',       // Social:Image
  SOCIAL_TITLE: 'K',       // Social:Title
  SOCIAL_DESCRIPTION: 'L', // Social:Description
} as const;

// 將 Google Sheets 原始資料轉換為 BlogPost 物件
export function transformBlogData(rawData: string[], index: number): BlogPost {
  const [
    title = '',
    body = '',
    hero = '',
    author = '',
    thumbnail = '',
    time = '',
    seoSlug = '',
    seoTitle = '',
    seoDescription = '',
    socialImage = '',
    socialTitle = '',
    socialDescription = ''
  ] = rawData;

  // 如果沒有 seoSlug，使用 index 生成
  const finalSlug = seoSlug || `post-${index}`;

  return {
    slug: finalSlug,
    title,
    body,
    hero,
    author,
    thumbnail,
    publishDate: time ? new Date(time) : new Date(),
    seoSlug,
    seoTitle,
    seoDescription,
    socialImage,
    socialTitle,
    socialDescription
  };
}

// 將 BlogPost 轉換為 BlogListItem
export function toBlogListItem(post: BlogPost): BlogListItem {
  return {
    slug: post.slug,
    title: post.title,
    thumbnail: post.thumbnail,
    publishDate: post.publishDate,
    author: post.author,
    seoDescription: post.seoDescription
  };
}

// 將優化的 Google Sheets 資料（只包含列表需要的欄位）直接轉換為 BlogListItem
export function transformBlogListData(rawData: string[], index: number): BlogListItem {
  const [
    title = '',        // A欄
    author = '',       // D欄
    thumbnail = '',    // E欄
    time = '',         // F欄
    seoSlug = '',      // G欄
    seoDescription = '' // I欄
  ] = rawData;

  // 如果沒有 seoSlug，使用 index 生成
  const finalSlug = seoSlug || `post-${index}`;

  return {
    slug: finalSlug,
    title,
    thumbnail,
    publishDate: time ? new Date(time) : new Date(),
    author,
    seoDescription
  };
}

// 格式化日期顯示
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

// 從 HTML 內容中提取純文字（用於摘要）
export function extractTextFromHtml(html: string, maxLength: number = 150): string {
  if (!html) return '';

  // 移除 HTML 標籤
  const text = html.replace(/<[^>]*>/g, '').trim();

  // 截取指定長度
  if (text.length <= maxLength) return text;

  return text.substring(0, maxLength) + '...';
}

// 從 HTML 作者欄位中提取作者名稱
export function extractAuthorName(authorHtml: string): string {
  if (!authorHtml) return '';

  // 移除 HTML 標籤，只保留文字
  return authorHtml.replace(/<[^>]*>/g, '').trim();
}
