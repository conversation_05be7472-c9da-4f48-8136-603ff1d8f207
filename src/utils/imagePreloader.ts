/**
 * 圖片預載入工具
 * 用於提前載入關鍵圖片，提升使用者體驗
 */

interface PreloadImageOptions {
  priority?: boolean;
  timeout?: number; // 載入超時時間（毫秒）
}

interface PreloadResult {
  success: boolean;
  url: string;
  error?: string;
}

/**
 * 預載入單張圖片
 */
export function preloadImage(
  url: string, 
  options: PreloadImageOptions = {}
): Promise<PreloadResult> {
  const { priority = false, timeout = 10000 } = options;

  return new Promise((resolve) => {
    const img = new Image();
    let timeoutId: NodeJS.Timeout;

    // 設置超時
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        resolve({
          success: false,
          url,
          error: 'Timeout'
        });
      }, timeout);
    }

    img.onload = () => {
      if (timeoutId) clearTimeout(timeoutId);
      resolve({
        success: true,
        url
      });
    };

    img.onerror = () => {
      if (timeoutId) clearTimeout(timeoutId);
      resolve({
        success: false,
        url,
        error: 'Load failed'
      });
    };

    // 設置優先級
    if (priority) {
      img.loading = 'eager';
    }

    img.src = url;
  });
}

/**
 * 批次預載入多張圖片
 */
export async function preloadImages(
  urls: string[],
  options: PreloadImageOptions = {}
): Promise<PreloadResult[]> {
  const promises = urls.map(url => preloadImage(url, options));
  return Promise.all(promises);
}

/**
 * 預載入關鍵圖片（優先級高）
 */
export function preloadCriticalImages(urls: string[]): Promise<PreloadResult[]> {
  return preloadImages(urls, { priority: true, timeout: 5000 });
}

/**
 * 背景預載入圖片（低優先級）
 */
export function preloadBackgroundImages(urls: string[]): Promise<PreloadResult[]> {
  return preloadImages(urls, { priority: false, timeout: 15000 });
}

/**
 * 智能圖片預載入
 * 根據網路狀況和裝置性能調整載入策略
 */
export function smartPreloadImages(urls: string[]): Promise<PreloadResult[]> {
  // 檢查網路狀況
  const connection = (navigator as unknown as { connection?: { effectiveType?: string; saveData?: boolean } }).connection;
  const isSlowConnection = connection && (
    connection.effectiveType === 'slow-2g' || 
    connection.effectiveType === '2g' ||
    connection.saveData
  );

  // 檢查是否為行動裝置
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  // 根據條件調整載入策略
  if (isSlowConnection) {
    // 慢速網路：只載入前幾張重要圖片
    return preloadImages(urls.slice(0, 2), { timeout: 20000 });
  } else if (isMobile) {
    // 行動裝置：載入一半的圖片
    return preloadImages(urls.slice(0, Math.ceil(urls.length / 2)), { timeout: 10000 });
  } else {
    // 桌面裝置且網路良好：載入所有圖片
    return preloadImages(urls, { timeout: 8000 });
  }
}

/**
 * 生成低品質圖片佔位符（LQIP）
 * 用於實現漸進式圖片載入
 */
export function generateLQIP(originalUrl: string, quality: number = 10): string {
  // 這裡可以實作圖片壓縮邏輯
  // 或者返回預先生成的低品質版本 URL
  
  // 簡單的實作：在 URL 中加入品質參數
  const url = new URL(originalUrl);
  url.searchParams.set('q', quality.toString());
  url.searchParams.set('w', '50'); // 寬度設為 50px
  return url.toString();
}

/**
 * 圖片載入性能監控
 */
export class ImageLoadingMonitor {
  private loadTimes: Map<string, number> = new Map();
  private startTimes: Map<string, number> = new Map();

  startMonitoring(url: string) {
    this.startTimes.set(url, performance.now());
  }

  endMonitoring(url: string) {
    const startTime = this.startTimes.get(url);
    if (startTime) {
      const loadTime = performance.now() - startTime;
      this.loadTimes.set(url, loadTime);
      this.startTimes.delete(url);
      
      // 記錄到控制台（開發環境）
      if (process.env.NODE_ENV === 'development') {
        console.log(`Image loaded: ${url} (${loadTime.toFixed(2)}ms)`);
      }
    }
  }

  getLoadTime(url: string): number | undefined {
    return this.loadTimes.get(url);
  }

  getAverageLoadTime(): number {
    const times = Array.from(this.loadTimes.values());
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  reset() {
    this.loadTimes.clear();
    this.startTimes.clear();
  }
}

// 全域圖片載入監控實例
export const imageMonitor = new ImageLoadingMonitor();
