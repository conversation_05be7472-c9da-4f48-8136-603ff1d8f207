import fs from 'fs';
import path from 'path';
import { FAQ, FAQListItem } from '@/types/faq';

// 記憶體快取
let faqCache: FAQ[] | null = null;
let faqListCache: FAQListItem[] | null = null;
let lastModified: number = 0;

/**
 * 讀取本地 FAQ JSON 檔案
 */
function readFAQFile(): FAQ[] {
  const faqPath = path.join(process.cwd(), 'src/data/faq.json');
  
  try {
    // 檢查檔案是否存在
    if (!fs.existsSync(faqPath)) {
      console.error('FAQ 檔案不存在:', faqPath);
      return [];
    }

    // 檢查檔案修改時間
    const stats = fs.statSync(faqPath);
    const currentModified = stats.mtime.getTime();

    // 如果檔案沒有修改且有快取，直接返回快取
    if (faqCache && currentModified === lastModified) {
      return faqCache;
    }

    // 讀取並解析 JSON 檔案
    const fileContent = fs.readFileSync(faqPath, 'utf-8');
    const faqs: FAQ[] = JSON.parse(fileContent);

    // 更新快取
    faqCache = faqs;
    lastModified = currentModified;

    console.log(`📖 從本地檔案讀取 FAQ 資料: ${faqs.length} 個問題`);
    return faqs;

  } catch (error) {
    console.error('讀取 FAQ 檔案失敗:', error);
    return [];
  }
}

/**
 * 獲取所有常見問題（完整資料，包含 answer）
 */
export function getAllFAQs(): FAQ[] {
  return readFAQFile();
}

/**
 * 獲取常見問題列表（只包含 question 和 tags，不包含 answer）
 */
export function getFAQList(): FAQListItem[] {
  // 檢查列表快取
  if (faqListCache && faqCache && lastModified > 0) {
    return faqListCache;
  }

  const faqs = readFAQFile();
  
  // 轉換為列表項目（移除 answer）
  const faqList: FAQListItem[] = faqs.map(faq => ({
    id: faq.id,
    question: faq.question,
    tags: faq.tags,
    rawTag: faq.rawTag
  }));

  // 更新列表快取
  faqListCache = faqList;

  return faqList;
}

/**
 * 根據 ID 獲取單一常見問題（完整資料）
 */
export function getFAQById(id: string): FAQ | null {
  const faqs = readFAQFile();
  return faqs.find(faq => faq.id === id) || null;
}

/**
 * 根據標籤篩選常見問題列表
 */
export function getFAQListByTag(tag: string): FAQListItem[] {
  const faqList = getFAQList();
  
  if (!tag || tag === 'all') {
    return faqList;
  }

  return faqList.filter(faq => 
    faq.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
  );
}

/**
 * 搜尋常見問題（搜尋 question 和 answer）
 */
export function searchFAQs(query: string): FAQ[] {
  if (!query.trim()) {
    return [];
  }

  const faqs = readFAQFile();
  const searchTerm = query.toLowerCase();

  return faqs.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm) ||
    faq.answer.toLowerCase().includes(searchTerm) ||
    faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

/**
 * 搜尋常見問題列表（只搜尋 question 和 tags，不搜尋 answer）
 */
export function searchFAQList(query: string): FAQListItem[] {
  if (!query.trim()) {
    return [];
  }

  const faqList = getFAQList();
  const searchTerm = query.toLowerCase();

  return faqList.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm) ||
    faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

/**
 * 獲取所有可用的標籤
 */
export function getAllTags(): string[] {
  const faqs = readFAQFile();
  const tagSet = new Set<string>();

  faqs.forEach(faq => {
    faq.tags.forEach(tag => tagSet.add(tag));
  });

  return Array.from(tagSet).sort();
}

/**
 * 清除快取（用於開發或測試）
 */
export function clearFAQCache(): void {
  faqCache = null;
  faqListCache = null;
  lastModified = 0;
  console.log('FAQ 快取已清除');
}
