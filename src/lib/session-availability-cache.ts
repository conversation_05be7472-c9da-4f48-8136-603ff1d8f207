/**
 * 場次名額快取管理
 */

// 快取變數
let cache: {
  data: Array<{
    sessionTime: string;
    registeredCount: number;
    availableSpots: number;
    showAvailability: boolean;
  }>;
  timestamp: number;
} | null = null;

// 快取持續時間（30秒）
const CACHE_DURATION = 30 * 1000;

/**
 * 取得快取資料
 */
export function getSessionAvailabilityCache() {
  if (!cache) return null;
  
  const now = Date.now();
  if (now - cache.timestamp > CACHE_DURATION) {
    cache = null;
    return null;
  }
  
  return cache.data;
}

/**
 * 設定快取資料
 */
export function setSessionAvailabilityCache(data: Array<{
  sessionTime: string;
  registeredCount: number;
  availableSpots: number;
  showAvailability: boolean;
}>) {
  cache = {
    data,
    timestamp: Date.now()
  };
}

/**
 * 清除快取（供其他 API 調用）
 */
export function clearSessionAvailabilityCache() {
  cache = null;
  console.log('🗑️ 場次名額快取已清除');
}

/**
 * 檢查快取是否有效
 */
export function isSessionAvailabilityCacheValid(): boolean {
  if (!cache) return false;
  
  const now = Date.now();
  return (now - cache.timestamp) <= CACHE_DURATION;
}
