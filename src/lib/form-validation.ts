/**
 * 表單驗證工具函數
 */

export interface FormData {
  name: string;
  email: string;
  phone: string;
  sessionTimes: string[];
  participationType: string;
  [key: string]: unknown;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * 驗證 email 格式
 */
export function validateEmail(email: string): boolean {
  if (!email || email.trim() === '') return false;

  // 更嚴格的 email 驗證
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // 檢查基本格式
  if (!emailRegex.test(email)) return false;

  // 檢查不允許的模式
  if (email.includes('..')) return false; // 連續點號
  if (email.startsWith('.') || email.endsWith('.')) return false; // 開頭或結尾是點號
  if (email.includes('@.') || email.includes('.@')) return false; // @ 旁邊是點號

  return true;
}

/**
 * 驗證電話號碼格式（支援台灣本地和國際格式）
 */
export function validatePhone(phone: string): boolean {
  // 台灣本地格式：09xxxxxxxx 或 0x-xxxxxxxx
  const localPhoneRegex = /^0[2-9]\d{8}$/;
  
  // 國際格式：+886xxxxxxxxx
  const internationalPhoneRegex = /^\+886[2-9]\d{8}$/;
  
  return localPhoneRegex.test(phone) || internationalPhoneRegex.test(phone);
}

/**
 * 驗證姓名格式
 */
export function validateName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 50;
}

/**
 * 驗證場次選擇
 */
export function validateSessionTimes(sessionTimes: string[]): boolean {
  return sessionTimes.length > 0;
}

/**
 * 驗證參與類型
 */
export function validateParticipationType(participationType: string): boolean {
  const validTypes = ['individual', 'pair'];
  return validTypes.includes(participationType);
}

/**
 * 完整表單資料驗證
 */
export function validateFormData(data: FormData): ValidationResult {
  const errors: Record<string, string> = {};

  // 驗證姓名
  if (!data.name || !validateName(data.name)) {
    errors.name = '請輸入有效的姓名（2-50個字元）';
  }

  // 驗證 email
  if (!data.email || !validateEmail(data.email)) {
    errors.email = '請輸入有效的電子郵件地址';
  }

  // 驗證電話
  if (!data.phone || !validatePhone(data.phone)) {
    errors.phone = '請輸入有效的電話號碼（如：0912345678 或 +886912345678）';
  }

  // 驗證場次選擇
  if (!data.sessionTimes || !validateSessionTimes(data.sessionTimes)) {
    errors.sessionTimes = '請選擇至少一個場次';
  }

  // 驗證參與類型
  if (!data.participationType || !validateParticipationType(data.participationType)) {
    errors.participationType = '請選擇參與類型';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * 檢查重複提交（基於 email 和時間間隔）
 */
export function checkDuplicateSubmission(
  email: string, 
  lastSubmissionTime?: string,
  intervalMinutes: number = 5
): boolean {
  if (!lastSubmissionTime) return false;
  
  const lastTime = new Date(lastSubmissionTime);
  const now = new Date();
  const diffMinutes = (now.getTime() - lastTime.getTime()) / (1000 * 60);
  
  return diffMinutes < intervalMinutes;
}

/**
 * 清理和標準化表單資料
 */
export function sanitizeFormData(data: FormData): FormData {
  return {
    ...data,
    name: data.name.trim(),
    email: data.email.trim().toLowerCase(),
    phone: data.phone.trim(),
    sessionTimes: data.sessionTimes.map(session => session.trim()),
    participationType: data.participationType.trim()
  };
}
