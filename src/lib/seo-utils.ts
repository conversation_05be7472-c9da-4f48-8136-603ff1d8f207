// 簡化的 SEO 工具函數
// 只處理核心的 metadata：title、description、社群分享縮圖、noindex

import { Metadata } from 'next';
import {
  DEFAULT_METADATA,
  SITE_CONFIG,
  PAGE_SEO_CONFIG
} from '@/config/seo-config';

// 生成靜態頁面的 metadata
export function generatePageMetadata(pageKey: string): Metadata {
  const pageConfig = PAGE_SEO_CONFIG[pageKey];

  if (!pageConfig) {
    return DEFAULT_METADATA;
  }

  const imageUrl = pageConfig.image || SITE_CONFIG.defaultImage;

  return {
    ...DEFAULT_METADATA,
    title: pageConfig.title,
    description: pageConfig.description,
    openGraph: {
      ...DEFAULT_METADATA.openGraph,
      title: pageConfig.title,
      description: pageConfig.description,
      url: `${SITE_CONFIG.url}/${pageKey === 'home' ? '' : pageKey}`,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: pageConfig.title,
        }
      ],
    },
    twitter: {
      ...DEFAULT_METADATA.twitter,
      title: pageConfig.title,
      description: pageConfig.description,
      images: [imageUrl],
    },
    robots: pageConfig.noindex ? {
      index: false,
      follow: false,
    } : {
      index: true,
      follow: true,
    },
  };
}

// 動態頁面的簡化 metadata 生成
export function generateDynamicMetadata(
  title: string,
  description: string,
  image?: string,
  path?: string
): Metadata {
  const imageUrl = image || SITE_CONFIG.defaultImage;
  const url = path ? `${SITE_CONFIG.url}${path}` : SITE_CONFIG.url;

  return {
    ...DEFAULT_METADATA,
    title,
    description,
    openGraph: {
      ...DEFAULT_METADATA.openGraph,
      title,
      description,
      url,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },
    twitter: {
      ...DEFAULT_METADATA.twitter,
      title,
      description,
      images: [imageUrl],
    },
  };
}
