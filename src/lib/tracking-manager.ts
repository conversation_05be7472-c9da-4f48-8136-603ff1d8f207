// 統一的追蹤事件管理系統
import {
  // sendCompleteRegistrationEvent,
  // sendLeadEvent,
  // sendPurchaseEvent,
  CAPIEventBuilder,
  sendServerEvent,
  getCAPIConfig,
  generateBusinessEventId,
  MetaEventName,
} from './meta-capi';

// 追蹤事件類型定義
export interface TrackingEventData {
  // 表單相關
  formName?: string;
  formStep?: string;

  // 商業相關
  value?: number;
  currency?: string;
  orderId?: string;

  // 產品相關
  productId?: string;
  productName?: string;
  category?: string;

  // 使用者相關
  userId?: string;
  email?: string;
  phone?: string;

  // 頁面相關
  pageTitle?: string;
  pageUrl?: string;
  referrer?: string;

  // 自定義屬性
  customData?: Record<string, unknown>;

  // 允許額外的字串索引
  [key: string]: unknown;
}

// 統一的追蹤管理器
export class TrackingManager {
  private request?: Request;
  private config = getCAPIConfig();

  constructor(request?: Request) {
    this.request = request;
  }

  // 發送瀏覽器端事件到 GTM (如果需要)
  private sendGTMEvent(eventName: string, eventData: Record<string, unknown>, eventId?: string) {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: eventName,
        event_id: eventId, // 與 CAPI 使用相同的 event_id 進行去重
        ...eventData,
      });
    }
  }

  // 發送伺服器端事件到 CAPI
  private async sendCAPIEvent(eventName: MetaEventName, eventData: TrackingEventData, eventId?: string) {
    if (!this.config.enabled || !this.request) {
      return null;
    }

    try {
      const userData = {
        ...(eventData.email && { em: eventData.email }),
        ...(eventData.phone && { ph: eventData.phone }),
        ...(eventData.userId && { external_id: eventData.userId }),
      };

      const customData = {
        ...(eventData.value && { value: eventData.value }),
        ...(eventData.currency && { currency: eventData.currency }),
        ...(eventData.orderId && { order_id: eventData.orderId }),
        ...(eventData.productName && { content_name: eventData.productName }),
        ...(eventData.category && { content_category: eventData.category }),
        ...(eventData.formName && { form_name: eventData.formName }),
        ...eventData.customData,
      };

      // 使用傳入的 eventId 或生成新的業務邏輯去重 ID
      const businessEventId = eventId || generateBusinessEventId(
        eventName,
        eventData.userId || eventData.email,
        eventData.orderId,
        eventData
      );

      const event = new CAPIEventBuilder(eventName)
        .setUserDataFromRequest(this.request)
        .setUserData(userData)
        .setSourceUrl(eventData.pageUrl || this.request.url)
        .setCustomData(customData)
        .setEventId(businessEventId) // 使用統一的 event_id 進行去重
        .build();

      return await sendServerEvent(event);
    } catch (error) {
      console.error('Failed to send CAPI event:', error);
      return null;
    }
  }

  // 統一的事件發送方法
  private async sendEvent(
    eventName: string,
    capiEventName: MetaEventName,
    eventData: TrackingEventData,
    sendToBoth = false
  ) {
    const results = {
      gtm: null as Record<string, unknown> | string | null,
      capi: null as Record<string, unknown> | null,
      event_id: null as string | null,
    };

    // 生成統一的事件 ID 用於去重
    const businessEventId = generateBusinessEventId(
      capiEventName,
      eventData.userId || eventData.email,
      eventData.orderId,
      eventData
    );
    results.event_id = businessEventId;

    // 發送到 GTM (瀏覽器端) - 使用相同的 event_id
    if (sendToBoth) {
      this.sendGTMEvent(eventName, eventData, businessEventId);
      results.gtm = 'sent';
    }

    // 發送到 CAPI (伺服器端) - 使用相同的 event_id
    results.capi = await this.sendCAPIEvent(capiEventName, eventData, businessEventId);

    return results;
  }

  // 頁面瀏覽事件 (主要透過 GTM，CAPI 可選)
  async trackPageView(data: TrackingEventData) {
    return this.sendEvent('page_view', 'PageView', data, false);
  }

  // 活動報名表單提交 (InitiateCheckout) - 主要使用
  async trackInitiateCheckout(data: TrackingEventData) {
    return this.sendEvent('initiate_checkout', 'InitiateCheckout', {
      ...data,
      value: data.value || 1500, // 預設活動價值
      currency: data.currency || 'TWD',
    });
  }

  // 會員註冊完成 (CompleteRegistration) - 如果有會員系統才使用
  async trackCompleteRegistration(data: TrackingEventData) {
    return this.sendEvent('complete_registration', 'CompleteRegistration', {
      ...data,
      value: data.value || 100,
      currency: data.currency || 'TWD',
    });
  }

  // 潛在客戶事件 (Lead)
  async trackLead(data: TrackingEventData) {
    return this.sendEvent('generate_lead', 'Lead', {
      ...data,
      value: data.value || 500,
      currency: data.currency || 'TWD',
    });
  }

  // 購買完成事件 (Purchase)
  async trackPurchase(data: TrackingEventData) {
    return this.sendEvent('purchase', 'Purchase', {
      ...data,
      currency: data.currency || 'TWD',
    });
  }

  // 產品瀏覽事件 (ViewContent)
  async trackViewContent(data: TrackingEventData) {
    return this.sendEvent('view_item', 'ViewContent', data, true);
  }
}

// 便利函數：在 API 路由中使用
export function createTrackingManager(request: Request) {
  return new TrackingManager(request);
}

// 便利函數：在客戶端使用 (只發送 GTM 事件)
export function trackClientEvent(eventName: string, eventData: Record<string, unknown>) {
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event: eventName,
      ...eventData,
    });
  }
}

// 類型聲明
declare global {
  interface Window {
    dataLayer: Record<string, unknown>[];
  }
}
