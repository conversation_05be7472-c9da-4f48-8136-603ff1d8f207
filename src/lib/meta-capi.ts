// Meta Conversions API 完整實作
import { META_PIXEL_CONFIG } from '@/config/environment-config';

// 標準 Meta 事件類型
export type MetaEventName =
  | 'PageView'
  | 'ViewContent'
  | 'Search'
  | 'AddToCart'
  | 'AddToWishlist'
  | 'InitiateCheckout'
  | 'AddPaymentInfo'
  | 'Purchase'
  | 'Lead'
  | 'CompleteRegistration'
  | 'Contact'
  | 'CustomizeProduct'
  | 'Donate'
  | 'FindLocation'
  | 'Schedule'
  | 'StartTrial'
  | 'SubmitApplication'
  | 'Subscribe'
  | 'AdImpression'
  | 'AdClick';

export interface UserData {
  em?: string; // Email (hashed)
  ph?: string; // Phone (hashed)
  fn?: string; // First name (hashed)
  ln?: string; // Last name (hashed)
  ct?: string; // City (hashed)
  st?: string; // State (hashed)
  zp?: string; // Zip code (hashed)
  country?: string; // Country code (ISO 3166-1 alpha-2)
  external_id?: string; // External ID (hashed)
  client_ip_address?: string;
  client_user_agent?: string;
  fbc?: string; // Facebook click ID
  fbp?: string; // Facebook browser ID
}

export interface CustomData {
  value?: number;
  currency?: string;
  content_name?: string;
  content_category?: string;
  content_ids?: string[];
  contents?: Array<{
    id: string;
    quantity: number;
    delivery_category?: string;
  }>;
  content_type?: string;
  order_id?: string;
  predicted_ltv?: number;
  num_items?: number;
  search_string?: string;
  status?: string;
  // 擴展自定義欄位
  page_title?: string;
  referrer?: string;
  form_name?: string;
  button_text?: string;
}

export interface ServerEvent {
  event_name: MetaEventName;
  event_time: number;
  event_id?: string;
  event_source_url?: string;
  action_source: 'website' | 'email' | 'app' | 'phone_call' | 'chat' | 'physical_store' | 'system_generated' | 'other';
  user_data: UserData;
  custom_data?: CustomData;
  opt_out?: boolean;
}

// CAPI 配置介面
export interface CAPIConfig {
  enabled: boolean;
  pixelId?: string;
  accessToken?: string;
  testEventCode?: string;
  enableRetry: boolean;
  maxRetries: number;
  timeout: number;
}

// 獲取 CAPI 配置
export function getCAPIConfig(): CAPIConfig {
  return {
    enabled: process.env.NEXT_PUBLIC_CAPI_ENABLED === 'true',
    pixelId: META_PIXEL_CONFIG.getPixelId(),
    accessToken: META_PIXEL_CONFIG.getAccessToken(),
    testEventCode: META_PIXEL_CONFIG.getTestEventCode(),
    enableRetry: true,
    maxRetries: 3,
    timeout: 10000,
  };
}

// 生成唯一事件 ID
export function generateEventId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 生成基於業務邏輯的事件 ID (用於去重)
export function generateBusinessEventId(
  eventType: string,
  userId?: string,
  orderId?: string,
  formData?: Record<string, unknown>
): string {
  const components = [
    eventType,
    userId || 'anonymous',
    orderId || '',
    formData?.email || '',
    Math.floor(Date.now() / (1000 * 60 * 5)) // 5分鐘內視為同一事件
  ].filter(Boolean);

  // 使用簡單的 hash 函數
  const hash = components.join('|');
  return `biz_${btoa(hash).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)}`;
}

// 事件建構器類
export class CAPIEventBuilder {
  private event: Partial<ServerEvent> = {
    action_source: 'website',
    event_time: Math.floor(Date.now() / 1000),
  };

  constructor(eventName: MetaEventName) {
    this.event.event_name = eventName;
    this.event.event_id = generateEventId();
  }

  // 設定使用者資料
  setUserData(userData: Partial<UserData>): this {
    this.event.user_data = { ...this.event.user_data, ...userData };
    return this;
  }

  // 設定自定義資料
  setCustomData(customData: Partial<CustomData>): this {
    this.event.custom_data = { ...this.event.custom_data, ...customData };
    return this;
  }

  // 設定事件來源 URL
  setSourceUrl(url: string): this {
    this.event.event_source_url = url;
    return this;
  }

  // 設定事件 ID (覆蓋自動生成的)
  setEventId(eventId: string): this {
    this.event.event_id = eventId;
    return this;
  }

  // 從請求中自動提取使用者資料
  setUserDataFromRequest(request: Request): this {
    const userAgent = request.headers.get('user-agent') || undefined;
    const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                     request.headers.get('x-real-ip') || undefined;

    this.setUserData({
      client_user_agent: userAgent,
      client_ip_address: clientIp,
    });

    return this;
  }

  // 建構事件
  build(): ServerEvent {
    if (!this.event.event_name) {
      throw new Error('Event name is required');
    }
    if (!this.event.user_data) {
      throw new Error('User data is required');
    }

    return this.event as ServerEvent;
  }
}

export async function sendServerEvent(event: ServerEvent, retryCount = 0): Promise<Record<string, unknown> | null> {
  const config = getCAPIConfig();

  // 檢查是否啟用 CAPI
  if (!config.enabled) {
    console.log('CAPI is disabled. Event not sent:', event.event_name);
    return null;
  }

  if (!config.pixelId || !config.accessToken) {
    console.warn('Meta Pixel ID or Access Token is not set. CAPI event not sent.');
    return null;
  }

  const url = `https://graph.facebook.com/v21.0/${config.pixelId}/events`;

  const payload = {
    data: [event],
    ...(config.testEventCode && { test_event_code: config.testEventCode }),
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.accessToken}`,
      },
      body: JSON.stringify(payload),
      // 設定超時時間
      signal: AbortSignal.timeout(config.timeout),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('Failed to send CAPI event:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData,
        event: event,
      });

      // 如果是暫時性錯誤且還有重試次數，則重試
      if (response.status >= 500 && retryCount < config.maxRetries && config.enableRetry) {
        console.log(`Retrying CAPI event (attempt ${retryCount + 1}/${config.maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // 指數退避
        return sendServerEvent(event, retryCount + 1);
      }

      throw new Error(`Failed to send CAPI event: ${response.status} ${response.statusText}`);
    }

    console.log('CAPI event sent successfully:', {
      eventName: event.event_name,
      eventId: event.event_id,
      responseData: responseData,
    });

    return responseData;

  } catch (error) {
    console.error('Error sending CAPI event:', {
      error: error instanceof Error ? error.message : String(error),
      event: event,
      retryCount,
    });

    // 如果是網路錯誤且還有重試次數，則重試
    if (retryCount < config.maxRetries && config.enableRetry && (
      error instanceof Error && (
        error.name === 'TimeoutError' ||
        error.message.includes('fetch')
      )
    )) {
      console.log(`Retrying CAPI event due to network error (attempt ${retryCount + 1}/${config.maxRetries})...`);
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // 指數退避
      return sendServerEvent(event, retryCount + 1);
    }

    // 最終失敗，但不要讓整個請求失敗
    return null;
  }
}

// 便利函數：發送頁面瀏覽事件
// 注意：PageView 在 Meta Events Manager 中可能顯示為「自訂事件」
// 這是 Meta 的正常行為，不影響事件追蹤和廣告優化
export async function sendPageViewEvent(
  request: Request,
  pageTitle?: string,
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('PageView')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      content_name: pageTitle || 'CAPI 測試頁面',
      content_category: 'website',
    })
    .build();

  return sendServerEvent(event);
}

// 便利函數：發送內容瀏覽事件（替代 PageView 的標準事件）
export async function sendViewContentEvent(
  request: Request,
  contentData: {
    contentName?: string;
    contentCategory?: string;
    contentType?: string;
    contentId?: string;
    value?: number;
    currency?: string;
  },
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('ViewContent')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      content_name: contentData.contentName,
      content_category: contentData.contentCategory,
      content_type: contentData.contentType || 'page',
      content_ids: contentData.contentId ? [contentData.contentId] : undefined,
      value: contentData.value,
      currency: contentData.currency || 'TWD',
    })
    .build();

  return sendServerEvent(event);
}

// 便利函數：發送購買事件
export async function sendPurchaseEvent(
  request: Request,
  orderData: {
    orderId: string;
    value: number;
    currency: string;
    items?: Array<{ id: string; quantity: number }>;
  },
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('Purchase')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      order_id: orderData.orderId,
      value: orderData.value,
      currency: orderData.currency,
      contents: orderData.items,
      num_items: orderData.items?.length,
    })
    .build();

  return sendServerEvent(event);
}

// 便利函數：發送註冊完成事件
export async function sendCompleteRegistrationEvent(
  request: Request,
  formData: {
    formName?: string;
    value?: number;
    currency?: string;
  },
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('CompleteRegistration')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      form_name: formData.formName,
      value: formData.value,
      currency: formData.currency,
    })
    .build();

  return sendServerEvent(event);
}

// 便利函數：發送潛在客戶事件
export async function sendLeadEvent(
  request: Request,
  leadData: {
    value?: number;
    currency?: string;
    contentName?: string;
  },
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('Lead')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      value: leadData.value,
      currency: leadData.currency,
      content_name: leadData.contentName,
    })
    .build();

  return sendServerEvent(event);
}

// 便利函數：發送開始結帳事件
export async function sendInitiateCheckoutEvent(
  request: Request,
  checkoutData: {
    value: number;
    currency?: string;
    contentName?: string;
    formName?: string;
    items?: Array<{ id: string; quantity: number }>;
  },
  userData?: Partial<UserData>
): Promise<Record<string, unknown> | null> {
  const event = new CAPIEventBuilder('InitiateCheckout')
    .setUserDataFromRequest(request)
    .setUserData(userData || {})
    .setSourceUrl(request.url)
    .setCustomData({
      value: checkoutData.value,
      currency: checkoutData.currency || 'TWD',
      content_name: checkoutData.contentName,
      form_name: checkoutData.formName,
      contents: checkoutData.items,
      num_items: checkoutData.items?.length,
    })
    .build();

  return sendServerEvent(event);
}
