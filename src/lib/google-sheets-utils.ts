/**
 * Google Sheets 資料處理工具函數
 * 提供資料格式化、解析和計算功能
 */

export interface SheetRowData {
  name: string;
  email: string;
  phone: string;
  sessionTime: string;
  participationType: string;
  submittedAt: string;
  orderNo: string;
  eventPrice: string;
  registrationStatus: string;
}

export interface SessionCapacityData {
  sessionTime: string;
  maxCapacity: number;
  registeredCount: number;
}

export interface SessionAvailability {
  sessionTime: string;
  maxCapacity: number;
  registeredCount: number;
  availableSpots: number;
  isAvailable: boolean;
  showAvailability: boolean;
}

/**
 * 格式化 Google Sheets 原始資料為結構化物件
 * @param rawData - 從 Google Sheets 讀取的原始二維陣列資料
 * @returns 格式化後的物件陣列
 */
export function formatSheetData(rawData: string[][]): SheetRowData[] {
  if (!rawData || rawData.length <= 1) {
    return [];
  }

  // 跳過標題行，處理資料行
  return rawData.slice(1).map(row => parseSheetRow(row));
}

/**
 * 解析單一工作表行資料
 * @param rowData - 單行資料陣列
 * @returns 解析後的物件
 */
export function parseSheetRow(rowData: (string | null | undefined)[]): SheetRowData {
  return {
    name: (rowData[2] || '').toString(),           // C欄位：姓名
    email: (rowData[4] || '').toString(),          // E欄位：電子郵件
    phone: (rowData[5] || '').toString(),          // F欄位：電話
    sessionTime: (rowData[1] || '').toString(),    // B欄位：場次時間
    participationType: (rowData[3] || '').toString(), // D欄位：參與類型
    submittedAt: (rowData[0] || '').toString(),    // A欄位：提交時間
    orderNo: (rowData[21] || '').toString(),       // V欄位：訂單編號
    eventPrice: (rowData[23] || '').toString(),    // X欄位：活動價格
    registrationStatus: (rowData[28] || '').toString() // AC欄位：報名狀態
  };
}

/**
 * 計算場次容量和可用性
 * @param sessionData - 場次基本資料
 * @returns 包含可用性資訊的完整場次資料
 */
export function calculateSessionCapacity(sessionData: SessionCapacityData): SessionAvailability {
  const { sessionTime, maxCapacity, registeredCount } = sessionData;

  const availableSpots = maxCapacity - registeredCount; // 允許負數，表示超額報名
  const isAvailable = availableSpots > 0;
  const showAvailability = availableSpots < 3 && availableSpots > 0; // 剩餘名額少於3時才顯示

  return {
    sessionTime,
    maxCapacity,
    registeredCount,
    availableSpots,
    isAvailable,
    showAvailability
  };
}

/**
 * 統計各場次的報名人數
 * @param registrationData - 報名資料陣列
 * @returns 各場次的統計資料
 */
export function calculateSessionStats(registrationData: string[][]): Record<string, number> {
  const sessionStats: Record<string, number> = {};

  // 報名狀態常數
  const REGISTRATION_STATUS = {
    CONFIRMED: 1,     // 已確認（計入名額）
    RESERVED: 2,      // 保留中（計入名額）
    CANCELLED: 3      // 已取消（不計入名額）
  };

  // 遍歷報名資料（跳過標題行）
  for (let i = 1; i < registrationData.length; i++) {
    const row = registrationData[i];
    if (!row || row.length < 29) continue; // 確保資料完整

    const sessionTimes = row[1] || ''; // B欄位：場次時間
    const participationType = row[3] || ''; // D欄位：參加方式
    const registrationStatus = parseInt(row[28] || '0'); // AC欄位：報名狀態

    // 只計算已確認或保留中的報名（排除已取消）
    if (registrationStatus === REGISTRATION_STATUS.CONFIRMED || registrationStatus === REGISTRATION_STATUS.RESERVED) {
      // 只計算第一個場次
      const firstSession = sessionTimes.split(',')[0]?.trim();

      if (firstSession) {
        if (!sessionStats[firstSession]) {
          sessionStats[firstSession] = 0;
        }
        
        // 雙人團報佔用 2 個名額
        const spotsUsed = participationType.includes('雙人') ? 2 : 1;
        sessionStats[firstSession] += spotsUsed;
      }
    }
  }

  return sessionStats;
}

/**
 * 驗證工作表資料格式
 * @param data - 工作表資料
 * @returns 驗證結果
 */
export function validateSheetData(data: string[][]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push('資料必須是陣列格式');
    return { isValid: false, errors };
  }

  if (data.length === 0) {
    errors.push('資料不能為空');
    return { isValid: false, errors };
  }

  // 檢查標題行
  if (data.length > 0 && (!Array.isArray(data[0]) || data[0].length === 0)) {
    errors.push('標題行格式不正確');
  }

  // 檢查資料行一致性
  if (data.length > 1) {
    const headerLength = data[0].length;
    for (let i = 1; i < data.length; i++) {
      if (!Array.isArray(data[i])) {
        errors.push(`第 ${i + 1} 行不是陣列格式`);
      } else if (data[i].length !== headerLength) {
        errors.push(`第 ${i + 1} 行欄位數量不一致`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 過濾和搜尋 FAQ 資料
 * @param faqs - FAQ 資料陣列
 * @param searchKeyword - 搜尋關鍵字
 * @param tagFilter - 標籤篩選
 * @returns 過濾後的 FAQ 資料
 */
export function filterFAQs(
  faqs: Array<{ question: string; answer: string; tags: string; order: string }>,
  searchKeyword?: string,
  tagFilter?: string
): Array<{ question: string; answer: string; tags: string; order: string }> {
  let filteredFAQs = [...faqs];

  // 標籤篩選
  if (tagFilter) {
    filteredFAQs = filteredFAQs.filter(faq => 
      faq.tags.includes(tagFilter)
    );
  }

  // 關鍵字搜尋
  if (searchKeyword) {
    filteredFAQs = filteredFAQs.filter(faq => 
      faq.question.includes(searchKeyword) || 
      faq.answer.includes(searchKeyword)
    );
  }

  // 按排序欄位排序
  filteredFAQs.sort((a, b) => {
    const orderA = parseInt(a.order) || 999;
    const orderB = parseInt(b.order) || 999;
    return orderA - orderB;
  });

  return filteredFAQs;
}

/**
 * 建立工作表範圍字串
 * @param sheetName - 工作表名稱
 * @param startColumn - 起始欄位
 * @param endColumn - 結束欄位
 * @param startRow - 起始行數（可選）
 * @param endRow - 結束行數（可選）
 * @returns 格式化的範圍字串
 */
export function createSheetRange(
  sheetName: string,
  startColumn: string,
  endColumn: string,
  startRow?: number,
  endRow?: number
): string {
  let range = `${sheetName}!${startColumn}`;
  
  if (startRow) {
    range += startRow;
  }
  
  range += `:${endColumn}`;
  
  if (endRow) {
    range += endRow;
  }

  return range;
}

/**
 * 轉換欄位索引為字母
 * @param index - 欄位索引（0-based）
 * @returns 欄位字母
 */
export function columnIndexToLetter(index: number): string {
  let result = '';
  while (index >= 0) {
    result = String.fromCharCode(65 + (index % 26)) + result;
    index = Math.floor(index / 26) - 1;
  }
  return result;
}

/**
 * 轉換欄位字母為索引
 * @param letter - 欄位字母
 * @returns 欄位索引（0-based）
 */
export function columnLetterToIndex(letter: string): number {
  let result = 0;
  for (let i = 0; i < letter.length; i++) {
    result = result * 26 + (letter.charCodeAt(i) - 64);
  }
  return result - 1;
}
