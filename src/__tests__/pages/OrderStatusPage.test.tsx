/**
 * 訂單查詢頁面組件測試
 * 測試訂單查詢功能和結果顯示
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';
import OrderStatusPage from '@/app/order/status/page';

// Define types for order data
interface OrderData {
  orderNo: string;
  paymentStatus: string;
  name: string;
  email: string;
}

// Mock OrderStatusResult component
jest.mock('@/components/OrderStatusResult', () => {
  return function MockOrderStatusResult({ orderData }: { orderData: OrderData }) {
    return (
      <div data-testid="order-status-result">
        <div data-testid="order-number">{orderData.orderNo}</div>
        <div data-testid="payment-status">{orderData.paymentStatus}</div>
        <div data-testid="order-details">
          <h3>訂單資訊</h3>
          <p>姓名: {orderData.name}</p>
          <p>電子郵件: {orderData.email}</p>
        </div>
        {/* 重新付款按鈕 - 只在待付款狀態顯示 */}
        {orderData.paymentStatus === '待付款' && (
          <button data-testid="retry-payment-button">重新付款</button>
        )}
      </div>
    );
  };
});

// Mock fetch
global.fetch = jest.fn();

describe('OrderStatusPage', () => {
  const user = userEvent.setup();

  const mockOrderData = {
    orderNo: 'pangea_test_123',
    name: '張測試',
    email: '<EMAIL>',
    phone: '0912345678',
    eventName: '錶匠體驗',
    eventPrice: 1500,
    sessionTimes: ['台北 07/19（六）13:20'],
    participationType: '個人參與',
    paymentStatus: '已付款',
    paymentMethod: '信用卡',
    paymentCompletedAt: '2024-01-15T10:30:00+08:00',
    submittedAt: '2024-01-15T09:00:00+08:00',
    sessionTime: '台北 07/20（日）13:20',
    participationType: 'individual',
    createdAt: '2024-01-15T10:30:00Z'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染頁面標題和描述', () => {
      render(<OrderStatusPage />);
      
      expect(screen.getByText('訂單查詢')).toBeInTheDocument();
      expect(screen.getByText('請輸入您的訂單號碼來查詢訂單狀態')).toBeInTheDocument();
    });

    test('應該顯示查詢表單', () => {
      render(<OrderStatusPage />);
      
      // 檢查表單元素
      expect(screen.getByLabelText(/訂單號碼/)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/例如：pangea_1234567890/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /查詢訂單/ })).toBeInTheDocument();
    });

    test('初始狀態不應該顯示查詢結果', () => {
      render(<OrderStatusPage />);
      
      expect(screen.queryByTestId('order-result')).not.toBeInTheDocument();
    });
  });

  describe('表單驗證測試', () => {
    test('應該要求輸入訂單號碼', async () => {
      render(<OrderStatusPage />);

      // 確保輸入欄位是空的
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      expect(orderNoInput).toHaveValue('');

      // 直接觸發表單提交事件，繞過瀏覽器原生驗證
      const form = orderNoInput.closest('form');
      if (form) {
        fireEvent.submit(form);
      }

      // 檢查錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('請輸入訂單號碼');
      });
    });

    test('應該驗證訂單號碼格式', async () => {
      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      // 輸入無效格式的訂單號碼
      await user.type(orderNoInput, '   ');
      await user.click(submitButton);
      
      // 檢查錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('請輸入訂單號碼');
      });
    });
  });

  describe('訂單查詢功能測試', () => {
    test('應該成功查詢存在的訂單', async () => {
      // Mock 成功的 API 回應
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockOrderData
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      // 輸入訂單號碼並查詢
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 驗證 API 調用
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/order-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ orderNo: 'pangea_test_123' })
        });
      });
      
      // 檢查查詢結果顯示
      await waitFor(() => {
        expect(screen.getByTestId('order-result')).toBeInTheDocument();
        expect(screen.getByText('pangea_test_123')).toBeInTheDocument();
        // 使用 getAllByText 來處理重複的文字
        expect(screen.getAllByText('已付款').length).toBeGreaterThan(0);
      });
    });

    test('應該處理訂單不存在的情況', async () => {
      // Mock 404 錯誤回應
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: '找不到此訂單' })
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'nonexistent_order');
      await user.click(submitButton);
      
      // 檢查錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent(/找不到此訂單/);
      });
    });

    test('應該處理網路錯誤', async () => {
      // Mock 網路錯誤
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Network error');
      });
    });

    test('應該處理伺服器錯誤', async () => {
      // Mock 500 錯誤回應
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: '伺服器錯誤' })
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent(/查詢失敗，請稍後再試/);
      });
    });
  });

  describe('載入狀態測試', () => {
    test('查詢期間應該顯示載入狀態', async () => {
      // Mock 延遲的 API 回應
      let resolvePromise: (value: Response) => void;
      const promise = new Promise<Response>(resolve => {
        resolvePromise = resolve;
      });
      (global.fetch as jest.Mock).mockReturnValueOnce(promise);

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查載入狀態
      expect(screen.getByText(/查詢中/)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      
      // 完成請求
      resolvePromise!({
        ok: true,
        json: async () => mockOrderData
      });
      
      // 檢查載入狀態消失
      await waitFor(() => {
        expect(screen.queryByText(/查詢中/)).not.toBeInTheDocument();
        expect(submitButton).toBeEnabled();
      });
    });
  });

  describe('重新付款功能測試', () => {
    test('待付款訂單應該顯示重新付款選項', async () => {
      const pendingOrderData = {
        ...mockOrderData,
        paymentStatus: '待付款'
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => pendingOrderData
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查重新付款按鈕出現
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /前往付款/ })).toBeInTheDocument();
      });
    });

    test('已付款訂單不應該顯示重新付款選項', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockOrderData
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查重新付款按鈕不出現
      await waitFor(() => {
        expect(screen.getByTestId('order-result')).toBeInTheDocument();
      });
      
      expect(screen.queryByRole('button', { name: /重新付款/ })).not.toBeInTheDocument();
    });
  });

  describe('表單重置測試', () => {
    test('新查詢應該清除之前的結果和錯誤', async () => {
      // 第一次查詢 - 錯誤
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: '找不到此訂單' })
      });

      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      
      await user.type(orderNoInput, 'nonexistent');
      await user.click(submitButton);
      
      // 檢查錯誤訊息顯示
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });
      
      // 第二次查詢 - 成功
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockOrderData
      });
      
      await user.clear(orderNoInput);
      await user.type(orderNoInput, 'pangea_test_123');
      await user.click(submitButton);
      
      // 檢查錯誤訊息消失，結果顯示
      await waitFor(() => {
        expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
        expect(screen.getByTestId('order-result')).toBeInTheDocument();
      });
    });
  });

  describe('可訪問性測試', () => {
    test('表單應該有正確的標籤和 ARIA 屬性', () => {
      render(<OrderStatusPage />);
      
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      expect(orderNoInput).toHaveAttribute('required');
      expect(orderNoInput).toHaveAttribute('name', 'orderNo');
      
      const submitButton = screen.getByRole('button', { name: /查詢訂單/ });
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    test('錯誤訊息應該與表單欄位關聯', async () => {
      render(<OrderStatusPage />);

      // 確保輸入欄位是空的
      const orderNoInput = screen.getByLabelText(/訂單號碼/);
      expect(orderNoInput).toHaveValue('');

      // 直接觸發表單提交事件，繞過瀏覽器原生驗證
      const form = orderNoInput.closest('form');
      if (form) {
        fireEvent.submit(form);
      }

      await waitFor(() => {
        const errorMessage = screen.getByTestId('error-message');
        expect(errorMessage).toBeInTheDocument();
        // 檢查錯誤訊息容器的結構，但不要求特定的 role 屬性
        expect(errorMessage).toHaveClass('bg-red-50');
      });
    });
  });
});
