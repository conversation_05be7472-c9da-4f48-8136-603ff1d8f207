/**
 * 錶匠體驗報名頁面組件測試
 * 測試表單渲染、驗證和提交功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';
import MovementAssemblingBookingPage from '@/app/movement-assembling-booking/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock scrollIntoView for JSDOM environment
Element.prototype.scrollIntoView = jest.fn();

// Mock alert for JSDOM environment
global.alert = jest.fn();

// Define types for session availability
interface SessionAvailability {
  registered: number;
  capacity: number;
  available: number;
}

interface AvailabilityData {
  [sessionName: string]: SessionAvailability;
}

// useSessionAvailability hook is mocked globally in jest.setup.js

// Mock fetch
global.fetch = jest.fn();

describe('MovementAssemblingBookingPage', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();

    (global.fetch as jest.Mock).mockImplementation((url: string, options?: RequestInit) => {
      // Mock event registration API
      if (url === '/api/event-registration') {
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true, orderNo: 'pangea_test_123' })
        });
      }

      // Default mock
      return Promise.resolve({
        ok: true,
        json: async () => ({ success: true })
      });
    });
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染頁面標題和描述', () => {
      render(<MovementAssemblingBookingPage />);

      expect(screen.getByText('錶匠體驗機芯拆解')).toBeInTheDocument();
      expect(screen.getByText(/錶匠體驗希望讓更多對機械錶有興趣的人/)).toBeInTheDocument();
    });

    test('應該顯示所有表單欄位', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      // 等待表單渲染
      await waitFor(() => {
        // 檢查基本資料欄位 - 使用 placeholder 或 name 屬性
        expect(screen.getByPlaceholderText('王大明')).toBeInTheDocument(); // 姓名欄位
        expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument(); // Email 欄位
        expect(screen.getByPlaceholderText('0912345678')).toBeInTheDocument(); // 手機欄位

        // 檢查場次選擇
        expect(screen.getByText('場次時間')).toBeInTheDocument();

        // 檢查參與類型
        expect(screen.getByText('參加方式')).toBeInTheDocument();
        expect(screen.getByText('個人報名')).toBeInTheDocument();
        expect(screen.getByText('雙人團報')).toBeInTheDocument();
      });
    });

    test('應該顯示場次選項和可用性資訊', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      // 等待表單完全加載
      await waitFor(() => {
        expect(screen.getByText('場次時間')).toBeInTheDocument();
      });

      // 檢查場次選項 - 使用更具體的選擇器，避免重複元素問題
      expect(screen.getAllByText(/台北 07\/20（日）13:20/).length).toBeGreaterThan(0);
      expect(screen.getAllByText(/台北 07\/20（日）15:20/).length).toBeGreaterThan(0);
      expect(screen.getAllByText(/台中 07\/18（五）19:20/).length).toBeGreaterThan(0);

      // 檢查可用性資訊 - 根據 mock 數據，只有剩餘 2 名額的場次會顯示
      await waitFor(() => {
        expect(screen.getByText('剩餘 2 名額')).toBeInTheDocument();
      });
    });

    test('應該顯示同意條款 checkbox', () => {
      render(<MovementAssemblingBookingPage />);
      
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      expect(agreeCheckbox).toBeInTheDocument();
      expect(agreeCheckbox).not.toBeChecked();
    });

    test('應該顯示提交按鈕', () => {
      render(<MovementAssemblingBookingPage />);
      
      const submitButton = screen.getByRole('button', { name: /提交報名/ });
      expect(submitButton).toBeInTheDocument();
      expect(submitButton).toBeDisabled(); // 初始狀態應該是禁用的
    });
  });

  describe('表單驗證測試', () => {
    test('應該在空表單提交時顯示驗證錯誤', async () => {
      render(<MovementAssemblingBookingPage />);

      const submitButton = screen.getByRole('button', { name: /提交報名/ });

      // 初始狀態按鈕應該禁用
      expect(submitButton).toBeDisabled();

      // 先同意條款，但由於其他必填欄位未填寫，按鈕仍應禁用
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      await user.click(agreeCheckbox);

      // 按鈕仍應該禁用，因為其他必填欄位未填寫
      expect(submitButton).toBeDisabled();

      // 這個測試主要驗證表單的初始狀態和基本驗證邏輯
    });

    test('應該驗證 email 格式', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      let emailInput, agreeCheckbox, submitButton;

      await waitFor(() => {
        emailInput = screen.getByPlaceholderText('<EMAIL>');
        agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
        submitButton = screen.getByRole('button', { name: /提交報名/ });
      });

      // 填寫無效的 email
      await user.type(emailInput, 'invalid-email');
      await user.click(agreeCheckbox);
      await user.click(submitButton);

      // 檢查 email 驗證錯誤 - 修正期望的錯誤訊息
      await waitFor(() => {
        expect(screen.getByText(/Email格式不正確/)).toBeInTheDocument();
      });
    });

    test('應該驗證電話號碼格式', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      let phoneInput, agreeCheckbox, submitButton;

      await waitFor(() => {
        phoneInput = screen.getByPlaceholderText('0912345678');
        agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
        submitButton = screen.getByRole('button', { name: /提交報名/ });
      });
      
      // 填寫無效的電話號碼
      await user.type(phoneInput, '123');
      await user.click(agreeCheckbox);
      await user.click(submitButton);
      
      // 檢查電話驗證錯誤 - 修正期望的錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-phone')).toHaveTextContent(/手機號碼格式不正確/);
      });
    });

    test('應該要求選擇場次', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      let nameInput, emailInput, phoneInput, agreeCheckbox, submitButton;

      await waitFor(() => {
        nameInput = screen.getByPlaceholderText('王大明');
        emailInput = screen.getByPlaceholderText('<EMAIL>');
        phoneInput = screen.getByPlaceholderText('0912345678');
        agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
        submitButton = screen.getByRole('button', { name: /提交報名/ });
      });

      // 填寫基本資料但不選擇場次
      await user.type(nameInput, '張測試');
      await user.type(emailInput, '<EMAIL>');
      await user.type(phoneInput, '0912345678');

      // 選擇參與類型
      await user.click(screen.getByRole('radio', { name: '個人報名' }));

      // 選擇性別
      await user.click(screen.getByRole('radio', { name: '男' }));

      // 選擇年齡
      await user.selectOptions(screen.getByDisplayValue('請選擇年齡'), '20-29');

      // 選擇居住地區
      await user.selectOptions(screen.getByDisplayValue('請選擇地區'), 'north');

      // 選擇手錶類型
      await user.click(screen.getByRole('checkbox', { name: '機械錶' }));

      // 填寫手錶品牌
      await user.type(screen.getByPlaceholderText('Rolex、Seiko......'), 'Rolex');

      await user.click(agreeCheckbox);

      // 由於沒有選擇場次，按鈕應該仍然禁用
      expect(submitButton).toBeDisabled();

      // 這個測試驗證了沒有選擇場次時，表單不能提交
      // 實際的錯誤訊息會在用戶嘗試提交時顯示，但由於按鈕禁用，無法觸發
    });
  });

  describe('表單互動測試', () => {
    test('應該能夠填寫基本資料', async () => {
      render(<MovementAssemblingBookingPage />);

      // 點擊立即報名按鈕來確保表單可見
      const signUpButton = screen.getByText('立即報名');
      await user.click(signUpButton);

      let nameInput, emailInput, phoneInput;

      await waitFor(() => {
        nameInput = screen.getByPlaceholderText('王大明') as HTMLInputElement;
        emailInput = screen.getByPlaceholderText('<EMAIL>') as HTMLInputElement;
        phoneInput = screen.getByPlaceholderText('0912345678') as HTMLInputElement;
      });

      await user.type(nameInput, '張測試');
      await user.type(emailInput, '<EMAIL>');
      await user.type(phoneInput, '0912345678');

      expect(nameInput.value).toBe('張測試');
      expect(emailInput.value).toBe('<EMAIL>');
      expect(phoneInput.value).toBe('0912345678');
    });

    test('應該能夠選擇場次', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const sessionCheckbox = screen.getByRole('checkbox', { 
        name: /台北 07\/20（日）13:20/ 
      });
      
      await user.click(sessionCheckbox);
      expect(sessionCheckbox).toBeChecked();
    });

    test('應該能夠選擇參與類型', async () => {
      render(<MovementAssemblingBookingPage />);

      const individualRadio = screen.getByRole('radio', { name: '個人報名' });
      const pairRadio = screen.getByRole('radio', { name: '雙人團報' });

      // 預設應該沒有選擇任何參與類型
      expect(individualRadio).not.toBeChecked();
      expect(pairRadio).not.toBeChecked();

      // 選擇個人參與
      await user.click(individualRadio);
      expect(individualRadio).toBeChecked();
      expect(pairRadio).not.toBeChecked();

      // 切換到雙人參與
      await user.click(pairRadio);
      expect(pairRadio).toBeChecked();
      expect(individualRadio).not.toBeChecked();
    });

    test('雙人參與時應該顯示同伴資料欄位', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const pairRadio = screen.getByRole('radio', { name: '雙人團報' });
      await user.click(pairRadio);
      
      // 檢查同伴資料欄位出現
      await waitFor(() => {
        expect(screen.getByText('同行者資訊')).toBeInTheDocument();
        expect(screen.getByText('同行者姓名')).toBeInTheDocument();
        expect(screen.getByText('同行者 Email')).toBeInTheDocument();
      });
    });

    test('填寫完整表單並同意條款後應該啟用提交按鈕', async () => {
      render(<MovementAssemblingBookingPage />);

      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      const submitButton = screen.getByRole('button', { name: /提交報名/ });

      // 初始狀態按鈕應該禁用
      expect(submitButton).toBeDisabled();

      // 填寫必填欄位
      await user.type(screen.getByPlaceholderText('王大明'), '張測試');
      await user.type(screen.getByPlaceholderText('<EMAIL>'), '<EMAIL>');
      await user.type(screen.getByPlaceholderText('0912345678'), '0912345678');

      // 選擇場次
      await user.click(screen.getByRole('checkbox', { name: /台北 07\/20（日）13:20/ }));

      // 選擇參與類型
      await user.click(screen.getByRole('radio', { name: '個人報名' }));

      // 選擇性別
      await user.click(screen.getByRole('radio', { name: '男' }));

      // 選擇年齡 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇年齡'), '20-29');

      // 選擇居住地區 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇地區'), 'north');

      // 選擇手錶類型
      await user.click(screen.getByRole('checkbox', { name: '機械錶' }));

      // 填寫手錶品牌
      await user.type(screen.getByPlaceholderText('Rolex、Seiko......'), 'Rolex');

      // 按鈕仍應該禁用（因為還沒同意條款）
      expect(submitButton).toBeDisabled();

      // 同意條款後按鈕應該啟用
      await user.click(agreeCheckbox);

      // 等待表單驗證完成
      await waitFor(() => {
        expect(submitButton).toBeEnabled();
      });
    });
  });

  describe('表單提交測試', () => {
    test('應該成功提交完整表單', async () => {
      render(<MovementAssemblingBookingPage />);

      // 填寫完整表單
      await user.type(screen.getByPlaceholderText('王大明'), '張測試');
      await user.type(screen.getByPlaceholderText('<EMAIL>'), '<EMAIL>');
      await user.type(screen.getByPlaceholderText('0912345678'), '0912345678');

      // 選擇場次
      await user.click(screen.getByRole('checkbox', {
        name: /台北 07\/20（日）13:20/
      }));

      // 選擇參與類型
      await user.click(screen.getByRole('radio', { name: '個人報名' }));

      // 選擇性別
      await user.click(screen.getByRole('radio', { name: '男' }));

      // 選擇年齡 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇年齡'), '20-29');

      // 選擇居住地區 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇地區'), 'north');

      // 選擇手錶類型
      await user.click(screen.getByRole('checkbox', { name: '機械錶' }));

      // 填寫手錶品牌
      await user.type(screen.getByPlaceholderText('Rolex、Seiko......'), 'Rolex');

      // 同意條款
      await user.click(screen.getByRole('checkbox', { name: /我同意/ }));

      // 等待按鈕啟用
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /提交報名/ })).toBeEnabled();
      });

      // 提交表單
      await user.click(screen.getByRole('button', { name: /提交報名/ }));

      // 驗證 API 調用
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/event-registration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('張測試')
        });
      });
    });

    test('應該處理提交錯誤', async () => {
      // Mock API 錯誤 - 只需要 mock event-registration API
      (global.fetch as jest.Mock).mockImplementation((url: string) => {
        if (url === '/api/event-registration') {
          return Promise.resolve({
            ok: false,
            json: async () => ({ success: false, error: '提交失敗' })
          });
        }
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true })
        });
      });

      render(<MovementAssemblingBookingPage />);

      // 填寫完整表單
      await user.type(screen.getByPlaceholderText('王大明'), '張測試');
      await user.type(screen.getByPlaceholderText('<EMAIL>'), '<EMAIL>');
      await user.type(screen.getByPlaceholderText('0912345678'), '0912345678');

      // 選擇場次
      await user.click(screen.getByRole('checkbox', {
        name: /台北 07\/20（日）13:20/
      }));

      // 選擇參與類型
      await user.click(screen.getByRole('radio', { name: '個人報名' }));

      // 選擇性別
      await user.click(screen.getByRole('radio', { name: '男' }));

      // 選擇年齡 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇年齡'), '20-29');

      // 選擇居住地區 - 使用 select 元素
      await user.selectOptions(screen.getByDisplayValue('請選擇地區'), 'north');

      // 選擇手錶類型
      await user.click(screen.getByRole('checkbox', { name: '機械錶' }));

      // 填寫手錶品牌
      await user.type(screen.getByPlaceholderText('Rolex、Seiko......'), 'Rolex');

      // 同意條款
      await user.click(screen.getByRole('checkbox', { name: /我同意/ }));

      // 等待按鈕啟用
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /提交報名/ })).toBeEnabled();
      });

      // 提交表單
      await user.click(screen.getByRole('button', { name: /提交報名/ }));

      // 檢查錯誤訊息顯示 - 錯誤訊息會通過 alert 顯示，不會在 DOM 中
      await waitFor(() => {
        // 驗證 API 被調用
        expect(global.fetch).toHaveBeenCalledWith('/api/event-registration', expect.any(Object));
      });

      // 由於錯誤是通過 alert 顯示的，我們無法直接測試 DOM 中的錯誤訊息
      // 但可以驗證 API 調用是否正確
    });
  });

  describe('響應式設計測試', () => {
    test('應該在行動裝置上正確顯示', () => {
      // 模擬行動裝置螢幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<MovementAssemblingBookingPage />);
      
      // 檢查表單在小螢幕上的佈局 - 使用表單元素查詢
      const nameInput = screen.getByPlaceholderText('王大明');
      expect(nameInput).toBeInTheDocument();

      // 檢查按鈕在行動版的樣式
      const submitButton = screen.getByRole('button', { name: /提交報名/ });
      expect(submitButton).toBeInTheDocument();
    });
  });
});
