import { http, HttpResponse } from 'msw';

// Mock Google Sheets API responses
export const googleSheetsHandlers = [
  // Mock successful sheet data append
  http.post('https://sheets.googleapis.com/v4/spreadsheets/:sheetId/values/:range:append', ({ request, params }) => {
    const { sheetId, range } = params;

    // 模擬不同的回應根據 range
    if (range?.includes('工作表1')) {
      return HttpResponse.json({
        spreadsheetId: sheetId,
        tableRange: 'A1:AD1000',
        updates: {
          spreadsheetId: sheetId,
          updatedRange: 'A1001:AD1001',
          updatedRows: 1,
          updatedColumns: 30,
          updatedCells: 30
        }
      });
    }

    return HttpResponse.json({
      spreadsheetId: sheetId,
      tableRange: 'A1:Z1000',
      updates: {
        spreadsheetId: sheetId,
        updatedRange: 'A1001:Z1001',
        updatedRows: 1,
        updatedColumns: 26,
        updatedCells: 26
      }
    });
  }),

  // Mock sheet data retrieval with different scenarios
  http.get('https://sheets.googleapis.com/v4/spreadsheets/:sheetId/values/:range', ({ params }) => {
    const { range } = params;

    // 模擬活動報名資料查詢
    if (range?.includes('工作表1!A:AC')) {
      return HttpResponse.json({
        range: 'A1:AC1000',
        majorDimension: 'ROWS',
        values: [
          ['提交時間', '場次時間', '姓名', '參與類型', '電子郵件', '電話', '同伴姓名', '同伴電子郵件', '性別', '年齡', '地區', '錶款類型', '錶款品牌', '問題', '同意條款', '自訂時間地點', '訂單編號', '活動名稱', '活動價格', '付款狀態', '付款方式', '付款完成時間', '交易編號', '退款狀態', '退款金額', 'UTM Source', 'UTM Medium', 'UTM Campaign', 'FBP', 'FBC', 'AC'],
          ['2025-01-01T00:00:00.000Z', '台北 07/20（日）13:20', '張測試', 'individual', '<EMAIL>', '0912345678', '', '', 'male', '25-34', '台北市', '機械錶', 'Rolex', '', 'true', '', 'pangea_test_123', '錶匠體驗機芯拆解', '3000', '已付款', '信用卡', '2025-01-01T12:00:00.000Z', 'payuni_123', '', '', 'facebook', 'cpc', 'watch-experience', 'fb.1.123', 'fb.1.123', '1'],
          ['2025-01-01T01:00:00.000Z', '台北 07/20（日）15:20', '李測試', 'individual', '<EMAIL>', '0987654321', '', '', 'female', '35-44', '台北市', '石英錶', 'Cartier', '', 'true', '', 'pangea_test_456', '錶匠體驗機芯拆解', '3000', '保留中', '', '', '', '', '', 'google', 'organic', '', '', '', '2']
        ]
      });
    }

    // 模擬 FAQ 資料查詢
    if (range?.includes('FAQ')) {
      return HttpResponse.json({
        range: 'FAQ!A1:D100',
        majorDimension: 'ROWS',
        values: [
          ['問題', '答案', '標籤', '排序'],
          ['什麼是錶匠體驗？', '錶匠體驗是一個讓您親手拆解機械錶機芯的活動...', '活動介紹', '1'],
          ['活動費用是多少？', '個人報名費用為 3000 元，雙人報名費用為 5000 元。', '費用,報名', '2']
        ]
      });
    }

    // 預設回應
    return HttpResponse.json({
      range: range || 'A1:Z1000',
      majorDimension: 'ROWS',
      values: [
        ['測試標題1', '測試標題2', '測試標題3'],
        ['測試資料1', '測試資料2', '測試資料3']
      ]
    });
  }),

  // Mock sheet data update
  http.put('https://sheets.googleapis.com/v4/spreadsheets/:sheetId/values/:range', () => {
    return HttpResponse.json({
      spreadsheetId: 'test-sheet-id',
      updatedRange: 'A1:AD1',
      updatedRows: 1,
      updatedColumns: 30,
      updatedCells: 30
    });
  }),

  // Mock Google Sheets API errors
  http.post('https://sheets.googleapis.com/v4/spreadsheets/error-sheet/values/:range:append', () => {
    return HttpResponse.json(
      {
        error: {
          code: 400,
          message: 'Invalid range',
          status: 'INVALID_ARGUMENT'
        }
      },
      { status: 400 }
    );
  }),
];

// Mock PayUni API responses
export const payuniHandlers = [
  // Mock payment creation - success
  http.post('https://sandbox-api.payuni.com.tw/api/upp', async ({ request }) => {
    const body = await request.text();
    const params = new URLSearchParams(body);

    // 檢查是否有必要參數
    if (!params.get('MerID') || !params.get('EncryptInfo') || !params.get('HashInfo')) {
      return HttpResponse.json({
        Status: 'FAIL',
        Message: '缺少必要參數'
      }, { status: 400 });
    }

    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '訂單建立成功',
      Result: {
        MerTradeNo: 'pangea_test_123',
        TradeNo: 'test_payuni_456',
        PaymentURL: 'https://sandbox-api.payuni.com.tw/api/payment/test_payuni_456'
      }
    });
  }),

  // Mock payment query - success
  http.post('https://sandbox-api.payuni.com.tw/api/trade/query', async ({ request }) => {
    const body = await request.text();
    const params = new URLSearchParams(body);

    const merID = params.get('MerID');
    const encryptInfo = params.get('EncryptInfo');

    if (!merID || !encryptInfo) {
      return HttpResponse.json({
        Status: 'FAIL',
        Message: '缺少必要參數'
      }, { status: 400 });
    }

    // 模擬不同的查詢結果
    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '查詢成功',
      EncryptInfo: 'mock_encrypted_response',
      HashInfo: 'mock_hash_response'
    });
  }),

  // Mock production PayUni endpoints
  http.post('https://api.payuni.com.tw/api/upp', () => {
    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '正式環境訂單建立成功',
      Result: {
        MerTradeNo: 'pangea_prod_123',
        TradeNo: 'prod_payuni_456',
        PaymentURL: 'https://api.payuni.com.tw/api/payment/prod_payuni_456'
      }
    });
  }),

  http.post('https://api.payuni.com.tw/api/trade/query', () => {
    return HttpResponse.json({
      Status: 'SUCCESS',
      Message: '正式環境查詢成功',
      EncryptInfo: 'prod_encrypted_response',
      HashInfo: 'prod_hash_response'
    });
  }),

  // Mock PayUni error responses
  http.post('https://sandbox-api.payuni.com.tw/api/upp/error', () => {
    return HttpResponse.json({
      Status: 'FAIL',
      Message: 'UPP02003 - 訂單編號長度超過限制'
    }, { status: 400 });
  }),

  // Mock PayUni timeout
  http.post('https://sandbox-api.payuni.com.tw/api/upp/timeout', () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(HttpResponse.json({
          Status: 'TIMEOUT',
          Message: '請求超時'
        }, { status: 408 }));
      }, 5000);
    });
  }),
];

// Mock Meta CAPI responses
export const capiHandlers = [
  // Mock successful CAPI event
  http.post('https://graph.facebook.com/v18.0/:pixelId/events', async ({ request, params }) => {
    const { pixelId } = params;
    const body = await request.json();

    // 驗證必要欄位
    if (!body.data || !Array.isArray(body.data) || body.data.length === 0) {
      return HttpResponse.json({
        error: {
          message: 'Invalid data format',
          type: 'OAuthException',
          code: 100
        }
      }, { status: 400 });
    }

    return HttpResponse.json({
      events_received: body.data.length,
      messages: [],
      fbtrace_id: `test-trace-${pixelId}-${Date.now()}`
    });
  }),

  // Mock CAPI error response
  http.post('https://graph.facebook.com/v18.0/error-pixel/events', () => {
    return HttpResponse.json({
      error: {
        message: 'Invalid access token',
        type: 'OAuthException',
        code: 190
      }
    }, { status: 401 });
  }),
];

// Mock external API responses
export const externalApiHandlers = [
  // Mock ngrok tunnel for webhook testing
  http.post('https://*.ngrok-free.app/api/webhook/payment', () => {
    return HttpResponse.json({
      success: true,
      message: 'Webhook received'
    });
  }),

  // Mock email service
  http.post('https://api.emailservice.com/send', () => {
    return HttpResponse.json({
      success: true,
      messageId: 'test-email-123'
    });
  }),
];

// Error simulation handlers
export const errorHandlers = [
  // Network error simulation
  http.post('https://network-error.test/api/*', () => {
    return HttpResponse.error();
  }),

  // Timeout simulation
  http.post('https://timeout.test/api/*', () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(HttpResponse.json({ message: 'Timeout' }, { status: 408 }));
      }, 10000);
    });
  }),
];

// Combine all handlers
export const handlers = [
  ...googleSheetsHandlers,
  ...payuniHandlers,
  ...capiHandlers,
  ...externalApiHandlers,
  ...errorHandlers,
];
