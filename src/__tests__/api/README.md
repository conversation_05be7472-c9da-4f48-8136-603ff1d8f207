# API 路由測試指南

本目錄包含 pangea-website 專案的完整 API 路由測試套件，專注於核心功能的單元測試和整合測試。

## 📋 測試架構

### 測試分類

1. **表單提交 API 測試** (`form-submission.test.ts`)
   - `/api/event-registration` - 活動報名表單
   - `/api/create-payment` - 付款訂單建立
   - 表單驗證、資料處理、錯誤處理

2. **PayUni 付款整合測試** (`payuni-integration.test.ts`)
   - `/api/payment/create` - 建立付款訂單
   - `/api/payment/callback` - 付款完成回調
   - `/api/payment/query` - 查詢訂單狀態
   - `/api/retry-payment` - 重新付款
   - `/api/webhook/payment` - PayUni webhook

3. **Google Sheets 整合測試** (`google-sheets-integration.test.ts`)
   - `/api/support` - FAQ 資料讀取
   - `/api/session-availability` - 場次容量控制
   - Google Sheets 讀寫操作
   - 容量控制邏輯

4. **邊緣案例和錯誤處理測試** (`edge-cases.test.ts`)
   - 驗證失敗情境
   - 付款重試邊緣案例
   - 容量限制邊界情況
   - 網路錯誤和超時處理
   - 資料一致性驗證

## 🚀 快速開始

### 安裝依賴

```bash
npm install
```

### 運行所有 API 測試

```bash
npm run test:api
```

### 運行特定測試套件

```bash
# 表單提交測試
npm run test:api:forms

# PayUni 付款整合測試
npm run test:api:payuni

# Google Sheets 整合測試
npm run test:api:sheets

# 邊緣案例測試
npm run test:api:edge
```

### 快速測試（核心功能）

```bash
npm run test:api:quick
```

### 覆蓋率報告

```bash
npm run test:api:coverage
```

## 🛠️ 測試工具

### MSW (Mock Service Worker)

使用 MSW 模擬外部 API 請求：

- **Google Sheets API** - 模擬資料讀寫操作
- **PayUni API** - 模擬付款建立和查詢
- **Meta CAPI** - 模擬事件追蹤
- **外部服務** - 模擬 webhook 和第三方服務

### 測試工具函數

位於 `../utils/test-helpers.ts`：

- `setupTestEnvironment()` - 設置測試環境變數
- `createTestApiRequest()` - 建立測試用 API 請求
- `parseResponse()` - 解析 API 回應
- `generateTestOrderNo()` - 生成測試訂單編號
- `generateTestEmail()` - 生成測試電子郵件

### 測試資料

位於 `../fixtures/form-data.ts`：

- `eventRegistrationTestData` - 活動報名測試資料
- `paymentTestData` - 付款測試資料
- `orderStatusTestData` - 訂單狀態測試資料
- `sheetsTestData` - Google Sheets 測試資料

## 📊 測試覆蓋範圍

### 核心功能測試

- ✅ 表單提交和驗證
- ✅ PayUni 付款流程
- ✅ Google Sheets 資料操作
- ✅ 場次容量控制
- ✅ 訂單狀態查詢
- ✅ 付款重試機制

### 邊緣案例測試

- ✅ 輸入驗證（XSS、長度限制、特殊字符）
- ✅ 並發處理（同時報名、重複提交）
- ✅ 錯誤處理（網路錯誤、API 錯誤、超時）
- ✅ 資料一致性（訂單編號唯一性、事務回滾）
- ✅ 容量限制（場次額滿、邊界情況）

### 整合測試

- ✅ API 路由端到端測試
- ✅ 外部服務整合測試
- ✅ 資料流完整性測試
- ✅ 錯誤恢復機制測試

## 🔧 配置說明

### 環境變數

測試會自動設置以下環境變數：

```bash
# PayUni 測試環境
PAYUNI_ENVIRONMENT=sandbox
PAYUNI_SANDBOX_MER_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=test_hash_key_32_characters_long
PAYUNI_SANDBOX_HASH_IV=test_hash_iv_16_chars

# Google Sheets 測試環境
GOOGLE_SHEETS_PRIVATE_KEY=test_private_key
GOOGLE_SHEETS_CLIENT_EMAIL=<EMAIL>
GOOGLE_SHEETS_SPREADSHEET_ID=test_sheet_id_123

# Meta CAPI 測試環境
META_PIXEL_ID=test_pixel_123
META_ACCESS_TOKEN=test_access_token_456
```

### Jest 配置

測試使用以下 Jest 配置：

- **測試環境**: jsdom
- **超時時間**: 30 秒
- **最大工作進程**: 1（避免並發問題）
- **覆蓋率閾值**: 70%

## 📝 編寫新測試

### 測試檔案結構

```typescript
import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse } from '../utils/test-helpers';

describe('新的 API 測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
    restoreEnv();
  });

  test('應該測試特定功能', async () => {
    // 測試實作
  });
});
```

### 最佳實踐

1. **使用描述性的測試名稱** - 清楚說明測試的目的
2. **測試正常和異常情況** - 包含成功和失敗的情境
3. **模擬真實的使用情境** - 使用真實的測試資料
4. **驗證完整的回應** - 檢查狀態碼、資料結構、錯誤訊息
5. **清理測試資料** - 確保測試間不互相影響

## 🐛 故障排除

### 常見問題

1. **MSW 未攔截請求**
   - 檢查 MSW 處理器是否正確配置
   - 確認請求 URL 和方法匹配

2. **環境變數未設置**
   - 確認 `setupTestEnvironment()` 被正確調用
   - 檢查環境變數名稱是否正確

3. **測試超時**
   - 增加測試超時時間
   - 檢查是否有未處理的 Promise

4. **記憶體洩漏**
   - 確保在 `afterAll` 中清理資源
   - 檢查是否有未關閉的連接

### 調試技巧

1. **啟用 MSW 調試**
   ```bash
   DEBUG_MSW=true npm run test:api
   ```

2. **查看詳細錯誤**
   ```bash
   npm run test:api -- --verbose
   ```

3. **運行單一測試**
   ```bash
   npx jest src/__tests__/api/form-submission.test.ts --testNamePattern="特定測試名稱"
   ```

## 📈 持續改進

### 測試指標

- **覆蓋率目標**: 80% 以上
- **測試執行時間**: 每個套件 < 30 秒
- **測試穩定性**: 99% 以上通過率

### 未來改進

- [ ] 增加效能測試
- [ ] 添加負載測試
- [ ] 實作 E2E 測試
- [ ] 增加視覺回歸測試
- [ ] 自動化測試報告

## 🤝 貢獻指南

1. 新增測試前先運行現有測試確保通過
2. 遵循現有的測試結構和命名慣例
3. 添加適當的註釋和文檔
4. 確保新測試有足夠的覆蓋率
5. 提交前運行完整的測試套件
