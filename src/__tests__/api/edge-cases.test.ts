/**
 * 邊緣案例和錯誤處理測試
 * 測試驗證失敗、付款重試、容量限制、網路錯誤等邊緣情境
 */

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse, generateTestEmail, generateTestOrderNo, delay } from '../utils/test-helpers';
import { eventRegistrationTestData } from '../fixtures/form-data';

// 導入驗證和工具函數進行單元測試
import { validateFormData, validateEmail, validatePhone } from '@/lib/form-validation';
import { convertTradeStatus, convertPaymentType } from '@/lib/payuni';
import { calculateSessionCapacity } from '@/lib/google-sheets-utils';

describe('邊緣案例和錯誤處理測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    // server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    // server.resetHandlers();
  });

  afterAll(() => {
    // server.close();
    restoreEnv();
  });

  describe('表單驗證邊緣案例', () => {
    test('應該拒絕包含惡意腳本的輸入', () => {
      const maliciousData = {
        ...eventRegistrationTestData.valid,
        name: '<script>alert("XSS")</script>',
        email: generateTestEmail(),
        questions: '<img src="x" onerror="alert(1)">'
      };

      const result = validateFormData(maliciousData);

      // 檢查是否包含惡意腳本
      expect(maliciousData.name).toContain('<script>');
      expect(maliciousData.questions).toContain('<img');

      // 驗證函數應該能正常處理（不會因為特殊字符而崩潰）
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
    });

    test('應該拒絕超長的輸入字串', () => {
      const longString = 'a'.repeat(10000);
      const longInputData = {
        ...eventRegistrationTestData.valid,
        name: longString,
        email: generateTestEmail()
      };

      const result = validateFormData(longInputData);

      // 檢查超長字串
      expect(longInputData.name.length).toBe(10000);
      expect(longInputData.name.length).toBeGreaterThan(1000);

      // 驗證函數應該能處理超長輸入
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
    });

    test('應該正確處理特殊字符', () => {
      const specialCharData = {
        ...eventRegistrationTestData.valid,
        name: '張測試 & 李測試',
        email: generateTestEmail(),
        watchBrands: 'Rolex & Omega, Patek Philippe',
        questions: '我想了解 "機芯拆解" 的詳細過程？'
      };

      const result = validateFormData(specialCharData);

      // 檢查特殊字符是否被正確處理
      expect(specialCharData.name).toContain('&');
      expect(specialCharData.watchBrands).toContain(',');
      expect(specialCharData.questions).toContain('"');

      // 驗證應該能正常處理特殊字符
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
    });

    test('應該正確處理空值和 undefined', () => {
      const emptyData = {
        name: '',
        email: '',
        phone: '',
        sessionTimes: [],
        participationType: '',
        agreeToTerms: false
      };

      const result = validateFormData(emptyData);

      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors).length).toBeGreaterThan(0);
      expect(result.errors).toHaveProperty('name');
      expect(result.errors).toHaveProperty('email');
      expect(result.errors).toHaveProperty('phone');
    });

    test('應該正確處理 null 和 undefined 值', () => {
      const nullData = {
        name: null,
        email: undefined,
        phone: null,
        sessionTimes: null,
        participationType: undefined
      };

      const result = validateFormData(nullData);

      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors).length).toBeGreaterThan(0);
    });
  });

  describe('付款重試邊緣案例', () => {
    test('應該正確處理多次重試付款資料', () => {
      const originalOrderNo = generateTestOrderNo();
      const newOrderNo1 = generateTestOrderNo();
      const newOrderNo2 = generateTestOrderNo();
      const email = generateTestEmail();

      // 檢查訂單編號的唯一性
      expect(originalOrderNo).not.toBe(newOrderNo1);
      expect(newOrderNo1).not.toBe(newOrderNo2);
      expect(originalOrderNo).not.toBe(newOrderNo2);

      // 檢查重試資料結構
      const retryData = {
        originalOrderNo,
        email,
        newOrderNo: newOrderNo1
      };

      expect(retryData.originalOrderNo).toMatch(/^pangea_test_/);
      expect(retryData.newOrderNo).toMatch(/^pangea_test_/);
      expect(validateEmail(retryData.email)).toBe(true);
    });

    test('應該防止過於頻繁的重試請求', () => {
      const originalOrderNo = generateTestOrderNo();
      const email = generateTestEmail();

      // 模擬多個重試請求的資料
      const retryRequests = Array.from({ length: 5 }, (_, i) => ({
        originalOrderNo,
        email,
        timestamp: Date.now() + i * 100, // 模擬時間間隔
        requestId: `retry_${i + 1}`
      }));

      // 檢查重試請求的資料結構
      expect(retryRequests.length).toBe(5);
      retryRequests.forEach((request, index) => {
        expect(request.originalOrderNo).toBe(originalOrderNo);
        expect(request.email).toBe(email);
        expect(request.requestId).toBe(`retry_${index + 1}`);
      });
    });

    test('應該正確處理訂單編號長度限制', () => {
      // PayUni 有訂單編號長度限制
      const longOrderNo = 'pangea_' + 'a'.repeat(100);
      const normalOrderNo = generateTestOrderNo();

      // 檢查長度限制
      expect(longOrderNo.length).toBeGreaterThan(50);
      expect(normalOrderNo.length).toBeLessThan(50);

      // 檢查格式
      expect(longOrderNo).toMatch(/^pangea_/);
      expect(normalOrderNo).toMatch(/^pangea_test_/);
    });
  });

  describe('容量限制邊緣案例', () => {
    test('應該正確處理場次容量邊界情況', () => {
      // 模擬場次即將額滿的情況
      const nearFullSession = {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 8,
        registeredCount: 7 // 只剩1個名額
      };

      const availability = calculateSessionCapacity(nearFullSession);

      expect(availability.availableSpots).toBe(1);
      expect(availability.isAvailable).toBe(true);
      expect(availability.showAvailability).toBe(true); // 剩餘名額少於3時顯示
    });

    test('應該正確處理同時報名衝突', () => {
      // 模擬多個用戶同時報名最後一個名額的情況
      const concurrentRegistrations = Array.from({ length: 3 }, (_, i) => ({
        email: generateTestEmail(),
        sessionTime: '台北 07/20（日）15:20',
        timestamp: Date.now() + i, // 模擬幾乎同時的請求
        userId: `user_${i + 1}`
      }));

      // 檢查並發報名資料
      expect(concurrentRegistrations.length).toBe(3);
      concurrentRegistrations.forEach((registration, index) => {
        expect(validateEmail(registration.email)).toBe(true);
        expect(registration.sessionTime).toBe('台北 07/20（日）15:20');
        expect(registration.userId).toBe(`user_${index + 1}`);
      });

      // 檢查時間戳的差異很小（模擬同時請求）
      const timestamps = concurrentRegistrations.map(r => r.timestamp);
      const maxTimeDiff = Math.max(...timestamps) - Math.min(...timestamps);
      expect(maxTimeDiff).toBeLessThan(10); // 時間差小於10毫秒
    });

    test('應該正確更新場次可用性快取', () => {
      // 模擬場次可用性的變化
      const initialSession = {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 8,
        registeredCount: 5
      };

      const afterRegistrationSession = {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 8,
        registeredCount: 6 // 新增一個報名
      };

      const initialAvailability = calculateSessionCapacity(initialSession);
      const updatedAvailability = calculateSessionCapacity(afterRegistrationSession);

      expect(initialAvailability.availableSpots).toBe(3);
      expect(updatedAvailability.availableSpots).toBe(2);
      expect(initialAvailability.showAvailability).toBe(false); // 3個名額不顯示
      expect(updatedAvailability.showAvailability).toBe(true);  // 2個名額要顯示
    });
  });

  describe('網路錯誤和超時處理', () => {
    test('應該正確處理超時錯誤', () => {
      const timeoutError = {
        type: 'TIMEOUT',
        message: '請求超時',
        code: 408,
        timestamp: Date.now()
      };

      expect(timeoutError.type).toBe('TIMEOUT');
      expect(timeoutError.code).toBe(408);
      expect(timeoutError.message).toContain('超時');
      expect(timeoutError.timestamp).toBeGreaterThan(0);
    });

    test('應該正確處理網路錯誤', () => {
      const networkError = {
        type: 'NETWORK_ERROR',
        message: '網路連線失敗',
        code: 500,
        service: 'PayUni API'
      };

      expect(networkError.type).toBe('NETWORK_ERROR');
      expect(networkError.code).toBe(500);
      expect(networkError.message).toContain('網路');
      expect(networkError.service).toBe('PayUni API');
    });

    test('應該正確處理服務不可用錯誤', () => {
      const serviceUnavailableError = {
        type: 'SERVICE_UNAVAILABLE',
        message: 'Google Sheets API 暫時不可用',
        code: 503,
        retryAfter: 60
      };

      expect(serviceUnavailableError.type).toBe('SERVICE_UNAVAILABLE');
      expect(serviceUnavailableError.code).toBe(503);
      expect(serviceUnavailableError.retryAfter).toBe(60);
    });
  });

  describe('資料一致性測試', () => {
    test('應該確保訂單編號唯一性', () => {
      const orderNos = new Set();

      // 生成多個訂單編號
      for (let i = 0; i < 10; i++) {
        const orderNo = generateTestOrderNo();
        expect(orderNos.has(orderNo)).toBe(false);
        orderNos.add(orderNo);
      }

      // 確保所有訂單編號都是唯一的
      expect(orderNos.size).toBe(10);
    });

    test('應該正確處理事務回滾情況', () => {
      const invalidData = {
        ...eventRegistrationTestData.valid,
        email: 'invalid-email-format'
      };

      const result = validateFormData(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveProperty('email');
      expect(result.errors.email).toContain('電子郵件');
    });

    test('應該正確處理重複提交檢測', () => {
      const duplicateData = {
        email: '<EMAIL>',
        sessionTime: '台北 07/20（日）13:20',
        timestamp1: Date.now(),
        timestamp2: Date.now() + 100 // 模擬快速重複提交
      };

      // 檢查重複提交的時間間隔
      const timeDiff = duplicateData.timestamp2 - duplicateData.timestamp1;
      expect(timeDiff).toBeLessThan(1000); // 1秒內的重複提交

      // 檢查相同的電子郵件和場次
      expect(duplicateData.email).toBe('<EMAIL>');
      expect(duplicateData.sessionTime).toBe('台北 07/20（日）13:20');
    });

    test('應該正確處理狀態轉換', () => {
      // 測試 PayUni 狀態轉換
      expect(convertTradeStatus('1')).toBe('已付款');
      expect(convertTradeStatus('2')).toBe('付款失敗');
      expect(convertTradeStatus('9')).toBe('未付款');

      expect(convertPaymentType('1')).toBe('信用卡');
      expect(convertPaymentType('2')).toBe('ATM轉帳');
    });
  });
});
