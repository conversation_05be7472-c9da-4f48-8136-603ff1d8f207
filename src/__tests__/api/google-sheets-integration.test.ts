/**
 * Google Sheets 整合測試
 * 測試資料讀取 API（活動資訊、FAQ 等）、資料寫入驗證、容量控制邏輯
 */

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse } from '../utils/test-helpers';
import { sheetsTestData } from '../fixtures/form-data';

// 導入 Google Sheets 工具函數進行單元測試
import { formatSheetData, parseSheetRow, calculateSessionCapacity } from '@/lib/google-sheets-utils';

describe('Google Sheets 整合測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    // server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    // server.resetHandlers();
  });

  afterAll(() => {
    // server.close();
    restoreEnv();
  });

  describe('Google Sheets 資料處理', () => {
    test('應該正確格式化工作表資料', () => {
      // 根據實際的 Google Sheets 欄位順序，建立完整的 29 個欄位
      const createFullRow = (submittedAt: string, sessionTime: string, name: string, participationType: string, email: string, phone: string, orderNo: string, eventPrice: string, registrationStatus: string) => {
        const row = new Array(29).fill('');
        row[0] = submittedAt;    // A欄位：提交時間
        row[1] = sessionTime;    // B欄位：場次時間
        row[2] = name;           // C欄位：姓名
        row[3] = participationType; // D欄位：參與類型
        row[4] = email;          // E欄位：電子郵件
        row[5] = phone;          // F欄位：電話
        row[21] = orderNo;       // V欄位：訂單編號
        row[23] = eventPrice;    // X欄位：活動價格
        row[28] = registrationStatus; // AC欄位：報名狀態
        return row;
      };

      const rawData = [
        ['提交時間', '場次時間', '姓名', '參與類型', '電子郵件', '電話', '...其他欄位...', 'AC欄位'],
        createFullRow('2025-01-01T00:00:00.000Z', '台北 07/20（日）13:20', '張測試', 'individual', '<EMAIL>', '0912345678', 'pangea_test_123', '3000', '1'),
        createFullRow('2025-01-01T01:00:00.000Z', '台北 07/20（日）15:20', '李測試', 'pair', '<EMAIL>', '0987654321', 'pangea_test_456', '5000', '2')
      ];

      const formattedData = formatSheetData(rawData);

      expect(Array.isArray(formattedData)).toBe(true);
      expect(formattedData.length).toBe(2); // 排除標題行
      expect(formattedData[0]).toHaveProperty('name', '張測試');
      expect(formattedData[0]).toHaveProperty('email', '<EMAIL>');
      expect(formattedData[0]).toHaveProperty('registrationStatus', '1');
    });

    test('應該正確解析工作表行資料', () => {
      // 建立完整的 29 個欄位的測試資料
      const rowData = new Array(29).fill('');
      rowData[0] = '2025-01-01T00:00:00.000Z';  // A欄位：提交時間
      rowData[1] = '台北 07/20（日）13:20';      // B欄位：場次時間
      rowData[2] = '張測試';                    // C欄位：姓名
      rowData[3] = 'individual';               // D欄位：參與類型
      rowData[4] = '<EMAIL>';         // E欄位：電子郵件
      rowData[5] = '0912345678';               // F欄位：電話
      rowData[21] = 'pangea_test_123';         // V欄位：訂單編號
      rowData[23] = '3000';                    // X欄位：活動價格
      rowData[28] = '1';                       // AC欄位：報名狀態

      const parsedRow = parseSheetRow(rowData);

      expect(parsedRow).toHaveProperty('name', '張測試');
      expect(parsedRow).toHaveProperty('email', '<EMAIL>');
      expect(parsedRow).toHaveProperty('phone', '0912345678');
      expect(parsedRow).toHaveProperty('sessionTime', '台北 07/20（日）13:20');
      expect(parsedRow).toHaveProperty('participationType', 'individual');
      expect(parsedRow).toHaveProperty('registrationStatus', '1');
    });

    test('應該正確處理空的資料行', () => {
      const emptyRowData = ['', '', '', '', '', '', '', '', ''];

      const parsedRow = parseSheetRow(emptyRowData);

      expect(parsedRow).toHaveProperty('name', '');
      expect(parsedRow).toHaveProperty('email', '');
      expect(parsedRow).toHaveProperty('registrationStatus', '');
    });

    test('應該正確處理不完整的資料行', () => {
      const incompleteRowData = ['2025-01-01T00:00:00.000Z', '台北 07/20（日）13:20', '張測試', 'individual', '<EMAIL>']; // 缺少其他欄位

      const parsedRow = parseSheetRow(incompleteRowData);

      expect(parsedRow).toHaveProperty('name', '張測試');
      expect(parsedRow).toHaveProperty('email', '<EMAIL>');
      expect(parsedRow).toHaveProperty('phone', '');
      expect(parsedRow).toHaveProperty('sessionTime', '台北 07/20（日）13:20');
    });
  });

  describe('FAQ 資料處理', () => {
    test('應該正確處理 FAQ 資料結構', () => {
      // 直接測試 FAQ 物件陣列，而不是使用 formatSheetData
      const faqData = [
        { question: '什麼是錶匠體驗？', answer: '錶匠體驗是一個讓您親手拆解機械錶機芯的活動...', tags: '活動介紹', order: '1' },
        { question: '活動費用是多少？', answer: '個人報名費用為 3000 元，雙人報名費用為 5000 元。', tags: '費用,報名', order: '2' }
      ];

      expect(Array.isArray(faqData)).toBe(true);
      expect(faqData.length).toBe(2);
      expect(faqData[0]).toHaveProperty('question', '什麼是錶匠體驗？');
      expect(faqData[0]).toHaveProperty('answer');
      expect(faqData[0]).toHaveProperty('tags', '活動介紹');
      expect(faqData[0]).toHaveProperty('order', '1');
    });

    test('應該正確處理標籤篩選邏輯', () => {
      const faqs = [
        { question: '問題1', answer: '答案1', tags: '活動介紹', order: '1' },
        { question: '問題2', answer: '答案2', tags: '費用,報名', order: '2' },
        { question: '問題3', answer: '答案3', tags: '活動介紹,時間', order: '3' }
      ];

      const filteredByTag = faqs.filter(faq => faq.tags.includes('活動介紹'));

      expect(filteredByTag.length).toBe(2);
      expect(filteredByTag[0].question).toBe('問題1');
      expect(filteredByTag[1].question).toBe('問題3');
    });

    test('應該正確處理關鍵字搜尋邏輯', () => {
      const faqs = [
        { question: '什麼是錶匠體驗？', answer: '錶匠體驗是一個讓您親手拆解機械錶機芯的活動...', tags: '活動介紹', order: '1' },
        { question: '活動費用是多少？', answer: '個人報名費用為 3000 元，雙人報名費用為 5000 元。', tags: '費用,報名', order: '2' }
      ];

      const searchKeyword = '錶匠體驗';
      const searchResults = faqs.filter(faq =>
        faq.question.includes(searchKeyword) || faq.answer.includes(searchKeyword)
      );

      expect(searchResults.length).toBe(1);
      expect(searchResults[0].question).toBe('什麼是錶匠體驗？');
    });

    test('應該正確處理空搜尋結果', () => {
      const faqs = [
        { question: '問題1', answer: '答案1', tags: '標籤1', order: '1' },
        { question: '問題2', answer: '答案2', tags: '標籤2', order: '2' }
      ];

      const searchKeyword = '不存在的關鍵字';
      const searchResults = faqs.filter(faq =>
        faq.question.includes(searchKeyword) || faq.answer.includes(searchKeyword)
      );

      expect(searchResults.length).toBe(0);
    });
  });

  describe('場次容量控制邏輯', () => {
    test('應該正確計算場次可用性', () => {
      const sessionData = {
        sessionTime: '台北 07/20（日）13:20',
        maxCapacity: 8,
        registeredCount: 5
      };

      const availability = calculateSessionCapacity(sessionData);

      expect(availability).toHaveProperty('sessionTime', '台北 07/20（日）13:20');
      expect(availability).toHaveProperty('maxCapacity', 8);
      expect(availability).toHaveProperty('registeredCount', 5);
      expect(availability).toHaveProperty('availableSpots', 3);
      expect(availability).toHaveProperty('isAvailable', true);
      expect(availability).toHaveProperty('showAvailability', false); // 剩餘3個名額不顯示
    });

    test('應該正確處理場次即將額滿的情況', () => {
      const sessionData = {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 8,
        registeredCount: 6
      };

      const availability = calculateSessionCapacity(sessionData);

      expect(availability.availableSpots).toBe(2);
      expect(availability.isAvailable).toBe(true);
      expect(availability.showAvailability).toBe(true); // 剩餘2個名額要顯示
    });

    test('應該正確處理場次已滿的情況', () => {
      const sessionData = {
        sessionTime: '台北 07/20（日）17:20',
        maxCapacity: 8,
        registeredCount: 8
      };

      const availability = calculateSessionCapacity(sessionData);

      expect(availability.availableSpots).toBe(0);
      expect(availability.isAvailable).toBe(false);
      expect(availability.showAvailability).toBe(false);
    });

    test('應該正確處理超額報名的情況', () => {
      const sessionData = {
        sessionTime: '台北 07/20（日）19:20',
        maxCapacity: 8,
        registeredCount: 10 // 超額報名
      };

      const availability = calculateSessionCapacity(sessionData);

      expect(availability.availableSpots).toBe(-2);
      expect(availability.isAvailable).toBe(false);
      expect(availability.showAvailability).toBe(false);
    });
  });

  describe('報名狀態統計邏輯', () => {
    test('應該正確統計不同報名狀態', () => {
      const registrationData = [
        { name: '用戶1', sessionTime: '台北 07/20（日）13:20', registrationStatus: '1' }, // 已確認
        { name: '用戶2', sessionTime: '台北 07/20（日）13:20', registrationStatus: '2' }, // 保留中
        { name: '用戶3', sessionTime: '台北 07/20（日）13:20', registrationStatus: '3' }, // 已取消
        { name: '用戶4', sessionTime: '台北 07/20（日）15:20', registrationStatus: '1' }, // 已確認
      ];

      const sessionCounts = registrationData.reduce((acc: Record<string, { confirmed: number; reserved: number; cancelled: number }>, reg) => {
        const session = reg.sessionTime;
        if (!acc[session]) {
          acc[session] = { confirmed: 0, reserved: 0, cancelled: 0 };
        }

        if (reg.registrationStatus === '1') acc[session].confirmed++;
        else if (reg.registrationStatus === '2') acc[session].reserved++;
        else if (reg.registrationStatus === '3') acc[session].cancelled++;

        return acc;
      }, {});

      expect(sessionCounts['台北 07/20（日）13:20'].confirmed).toBe(1);
      expect(sessionCounts['台北 07/20（日）13:20'].reserved).toBe(1);
      expect(sessionCounts['台北 07/20（日）13:20'].cancelled).toBe(1);
      expect(sessionCounts['台北 07/20（日）15:20'].confirmed).toBe(1);
    });

    test('應該正確處理場次時間格式驗證', () => {
      const validSessionFormats = [
        '台北 07/20（日）13:20',
        '台中 07/21（一）15:30',
        '有意願但無合適時間地點'
      ];

      validSessionFormats.forEach(sessionTime => {
        expect(typeof sessionTime).toBe('string');
        expect(sessionTime.length).toBeGreaterThan(0);

        const hasValidFormat = ['台北', '台中', '有意願但無合適時間地點'].some(format =>
          sessionTime.includes(format)
        );
        expect(hasValidFormat).toBe(true);
      });
    });

    test('應該正確處理多場次報名邏輯', () => {
      const multiSessionString = '台北 07/20（日）13:20,台北 07/20（日）15:20';
      const sessionArray = multiSessionString.split(',');
      const firstSession = sessionArray[0];

      expect(sessionArray.length).toBe(2);
      expect(firstSession).toBe('台北 07/20（日）13:20');
      expect(sessionArray[1]).toBe('台北 07/20（日）15:20');

      // 只計算第一個場次的邏輯
      const countedSession = firstSession;
      expect(countedSession).toBe('台北 07/20（日）13:20');
    });
  });

  describe('資料驗證和錯誤處理', () => {
    test('應該正確識別錯誤的資料格式', () => {
      // 根據實際的欄位順序建立測試資料
      const invalidData = [
        ['提交時間', '場次時間', '姓名', '參與類型', '電子郵件', '電話'],
        ['', '', null, undefined, '', ''], // 無效資料
        ['', '', '', '', 'invalid-email', '123'] // 格式錯誤
      ];

      const formattedData = formatSheetData(invalidData);

      expect(Array.isArray(formattedData)).toBe(true);
      expect(formattedData.length).toBe(2);

      // 檢查無效資料的處理
      expect(formattedData[0].name).toBe('');
      expect(formattedData[0].email).toBe('');
      expect(formattedData[1].name).toBe('');
      expect(formattedData[1].email).toBe('invalid-email');
    });

    test('應該正確處理範圍查詢格式', () => {
      const validRanges = [
        '工作表1!A:AC',
        '工作表1!B:D',
        'FAQ!A1:D100',
        '工作表1!A1:Z1000'
      ];

      validRanges.forEach(range => {
        expect(typeof range).toBe('string');
        expect(range).toContain('!');
        expect(range.split('!').length).toBe(2);
      });
    });

    test('應該正確處理批次資料格式', () => {
      const batchData = [
        ['用戶1', '<EMAIL>', '0912345678'],
        ['用戶2', '<EMAIL>', '0987654321'],
        ['用戶3', '<EMAIL>', '0911111111']
      ];

      expect(Array.isArray(batchData)).toBe(true);
      expect(batchData.length).toBe(3);

      batchData.forEach(row => {
        expect(Array.isArray(row)).toBe(true);
        expect(row.length).toBe(3);
        expect(typeof row[0]).toBe('string'); // 姓名
        expect(typeof row[1]).toBe('string'); // 電子郵件
        expect(typeof row[2]).toBe('string'); // 電話
      });
    });

    test('應該正確處理大量資料的效能', () => {
      // 模擬大量資料處理
      const largeDataSet: string[][] = Array.from({ length: 1000 }, (_, i) => [
        `用戶${i + 1}`,
        `test${i + 1}@example.com`,
        `091234567${i % 10}`,
        '台北 07/20（日）13:20',
        'individual',
        new Date().toISOString(),
        `pangea_test_${i + 1}`,
        '3000',
        '1'
      ]);

      const startTime = Date.now();
      const processedData = formatSheetData([
        ['姓名', '電子郵件', '電話', '場次時間', '參與類型', '提交時間', '訂單編號', '價格', 'AC欄位'],
        ...largeDataSet
      ]);
      const endTime = Date.now();

      expect(processedData.length).toBe(1000);
      expect(endTime - startTime).toBeLessThan(1000); // 應該在1秒內完成
    });
  });
});
