/**
 * 表單提交 API 路由測試
 * 測試 /api/register, /api/contact, /api/booking 等表單提交端點
 */

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from '../mocks/server';
import { setupTestEnvironment, createTestApiRequest, parseResponse, generateTestEmail, generateTestOrderNo } from '../utils/test-helpers';
import { eventRegistrationTestData, paymentTestData } from '../fixtures/form-data';

// 導入表單驗證函數進行單元測試
import { validateFormData, validateEmail, validatePhone } from '@/lib/form-validation';

describe('表單提交 API 路由測試', () => {
  let restoreEnv: () => void;

  beforeAll(() => {
    // server.listen();
    restoreEnv = setupTestEnvironment();
  });

  afterEach(() => {
    // server.resetHandlers();
  });

  afterAll(() => {
    // server.close();
    restoreEnv();
  });

  describe('表單驗證功能測試', () => {
    test('應該正確驗證有效的表單資料', () => {
      const testData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail()
      };

      const result = validateFormData(testData);

      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    test('應該拒絕缺少必要欄位的表單', () => {
      const invalidData = {
        name: '測試用戶',
        email: '',
        phone: '',
        sessionTimes: [],
        participationType: ''
      };

      const result = validateFormData(invalidData);

      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors).length).toBeGreaterThan(0);
      expect(result.errors).toHaveProperty('email');
      expect(result.errors).toHaveProperty('phone');
      expect(result.errors).toHaveProperty('sessionTimes');
      expect(result.errors).toHaveProperty('participationType');
    });

    test('應該正確驗證雙人報名資料', () => {
      const pairData = {
        ...eventRegistrationTestData.pairRegistration,
        email: generateTestEmail(),
        companionEmail: generateTestEmail()
      };

      const result = validateFormData(pairData);

      expect(result.isValid).toBe(true);
      expect(pairData.participationType).toBe('pair');
      expect(pairData.companionName).toBeTruthy();
      expect(validateEmail(pairData.companionEmail)).toBe(true);
    });

    test('應該正確處理自訂時間地點', () => {
      const customData = {
        ...eventRegistrationTestData.customTime,
        email: generateTestEmail()
      };

      expect(customData.sessionTimes).toContain('有意願但無合適時間地點');
      expect(customData.customTimeLocation).toBeTruthy();
      expect(customData.customTimeLocation).toBe('希望在台中舉辦，週末時間');
    });

    test('應該正確驗證電子郵件格式', () => {
      // 測試有效的電子郵件格式
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);

      // 測試無效的電子郵件格式
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });

    test('應該正確驗證電話號碼格式', () => {
      // 測試有效的電話號碼格式（根據實際的 validatePhone 函數）
      expect(validatePhone('0912345678')).toBe(true);
      expect(validatePhone('+886912345678')).toBe(true);
      expect(validatePhone('0223456789')).toBe(true); // 市話格式

      // 測試無效的電話號碼格式
      expect(validatePhone('123')).toBe(false);
      expect(validatePhone('abcdefghij')).toBe(false);
      expect(validatePhone('')).toBe(false);
      expect(validatePhone('1234567890123')).toBe(false); // 太長
      expect(validatePhone('09-1234-5678')).toBe(false); // 有分隔符號
      expect(validatePhone('************')).toBe(false); // 有空格
    });

    test('應該正確處理 UTM 追蹤參數', () => {
      const utmData = {
        ...eventRegistrationTestData.valid,
        email: generateTestEmail(),
        utmParams: {
          utmSource: 'facebook',
          utmMedium: 'cpc',
          utmCampaign: 'watch-experience',
          fbp: 'fb.1.1234567890.1234567890',
          fbc: 'fb.1.1234567890.1234567890'
        }
      };

      expect(utmData.utmParams).toHaveProperty('utmSource', 'facebook');
      expect(utmData.utmParams).toHaveProperty('utmMedium', 'cpc');
      expect(utmData.utmParams).toHaveProperty('utmCampaign', 'watch-experience');
      expect(utmData.utmParams).toHaveProperty('fbp');
      expect(utmData.utmParams).toHaveProperty('fbc');
    });
  });

  describe('資料結構驗證測試', () => {
    test('應該正確處理重複的電子郵件檢測', () => {
      const email1 = '<EMAIL>';
      const email2 = '<EMAIL>';
      const email3 = '<EMAIL>';

      expect(email1).toBe(email2);
      expect(email1).not.toBe(email3);
      expect(validateEmail(email1)).toBe(true);
      expect(validateEmail(email2)).toBe(true);
      expect(validateEmail(email3)).toBe(true);
    });

    test('應該正確處理場次容量資料結構', () => {
      const sessionData = {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 8,
        registeredCount: 7,
        availableSpots: 1,
        isAvailable: true,
        showAvailability: true // 剩餘名額少於3時顯示
      };

      expect(sessionData.availableSpots).toBe(sessionData.maxCapacity - sessionData.registeredCount);
      expect(sessionData.isAvailable).toBe(sessionData.availableSpots > 0);
      expect(sessionData.showAvailability).toBe(sessionData.availableSpots < 3 && sessionData.availableSpots > 0);
    });

    test('應該正確生成測試資料', () => {
      const orderNo1 = generateTestOrderNo();
      const orderNo2 = generateTestOrderNo();
      const email1 = generateTestEmail();
      const email2 = generateTestEmail();

      // 確保生成的資料是唯一的
      expect(orderNo1).not.toBe(orderNo2);
      expect(email1).not.toBe(email2);

      // 確保格式正確
      expect(orderNo1).toMatch(/^pangea_test_\d+_[a-z0-9]+$/);
      expect(orderNo2).toMatch(/^pangea_test_\d+_[a-z0-9]+$/);
      expect(validateEmail(email1)).toBe(true);
      expect(validateEmail(email2)).toBe(true);
    });
  });
});
