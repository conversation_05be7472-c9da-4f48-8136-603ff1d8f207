#!/usr/bin/env node

/**
 * API 路由測試運行腳本
 * 提供便捷的測試執行和報告功能
 */

const { execSync } = require('child_process');
const path = require('path');

// 測試配置
const TEST_CONFIG = {
  // 測試套件定義
  suites: {
    'form-submission': {
      name: '表單提交 API 測試',
      pattern: 'src/__tests__/api/form-submission.test.ts',
      priority: 1
    },
    'payuni-integration': {
      name: 'PayUni 付款整合測試',
      pattern: 'src/__tests__/api/payuni-integration.test.ts',
      priority: 2
    },
    'google-sheets': {
      name: 'Google Sheets 整合測試',
      pattern: 'src/__tests__/api/google-sheets-integration.test.ts',
      priority: 3
    },
    'edge-cases': {
      name: '邊緣案例和錯誤處理測試',
      pattern: 'src/__tests__/api/edge-cases.test.ts',
      priority: 4
    },
    'all-api': {
      name: '所有 API 路由測試',
      pattern: 'src/__tests__/api/*.test.ts',
      priority: 5
    }
  },

  // Jest 選項
  jestOptions: {
    verbose: true,
    coverage: false,
    detectOpenHandles: true,
    forceExit: true,
    maxWorkers: 1 // 避免並發問題
  }
};

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  const border = '='.repeat(60);
  console.log(colorize(border, 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize(border, 'cyan'));
}

function printSection(title) {
  console.log(colorize(`\n📋 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(40), 'blue'));
}

function printSuccess(message) {
  console.log(colorize(`✅ ${message}`, 'green'));
}

function printError(message) {
  console.log(colorize(`❌ ${message}`, 'red'));
}

function printWarning(message) {
  console.log(colorize(`⚠️  ${message}`, 'yellow'));
}

function printInfo(message) {
  console.log(colorize(`ℹ️  ${message}`, 'cyan'));
}

// 執行 Jest 命令
function runJest(pattern, options = {}) {
  const jestConfig = {
    ...TEST_CONFIG.jestOptions,
    ...options
  };

  const jestArgs = [
    pattern,
    jestConfig.verbose ? '--verbose' : '',
    jestConfig.coverage ? '--coverage' : '',
    jestConfig.detectOpenHandles ? '--detectOpenHandles' : '',
    jestConfig.forceExit ? '--forceExit' : '',
    jestConfig.maxWorkers ? `--maxWorkers=${jestConfig.maxWorkers}` : '',
    '--testTimeout=30000', // 30秒超時
    '--silent=false'
  ].filter(Boolean).join(' ');

  const command = `npx jest ${jestArgs}`;
  
  printInfo(`執行命令: ${command}`);
  
  try {
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: {
        ...process.env,
        NODE_ENV: 'test',
        DEBUG_MSW: 'false' // 關閉 MSW 調試輸出
      }
    });
    return true;
  } catch (error) {
    return false;
  }
}

// 運行特定測試套件
function runTestSuite(suiteName) {
  const suite = TEST_CONFIG.suites[suiteName];
  
  if (!suite) {
    printError(`未找到測試套件: ${suiteName}`);
    printInfo('可用的測試套件:');
    Object.keys(TEST_CONFIG.suites).forEach(name => {
      console.log(`  - ${name}: ${TEST_CONFIG.suites[name].name}`);
    });
    return false;
  }

  printSection(`運行測試套件: ${suite.name}`);
  return runJest(suite.pattern);
}

// 運行所有測試套件（按優先級順序）
function runAllTestSuites() {
  printHeader('API 路由測試 - 完整測試套件');
  
  const suites = Object.entries(TEST_CONFIG.suites)
    .filter(([name]) => name !== 'all-api') // 排除 all-api 套件
    .sort(([, a], [, b]) => a.priority - b.priority);

  let totalPassed = 0;
  let totalFailed = 0;

  for (const [suiteName, suite] of suites) {
    printSection(`${suite.priority}. ${suite.name}`);
    
    const success = runJest(suite.pattern);
    
    if (success) {
      printSuccess(`${suite.name} 測試通過`);
      totalPassed++;
    } else {
      printError(`${suite.name} 測試失敗`);
      totalFailed++;
    }
    
    console.log(''); // 空行分隔
  }

  // 測試結果摘要
  printSection('測試結果摘要');
  console.log(`總測試套件: ${totalPassed + totalFailed}`);
  printSuccess(`通過: ${totalPassed}`);
  if (totalFailed > 0) {
    printError(`失敗: ${totalFailed}`);
  }

  return totalFailed === 0;
}

// 運行覆蓋率測試
function runCoverageTest() {
  printHeader('API 路由測試 - 覆蓋率報告');
  
  return runJest('src/__tests__/api/*.test.ts', {
    coverage: true,
    verbose: false
  });
}

// 運行快速測試（只測試核心功能）
function runQuickTest() {
  printHeader('API 路由測試 - 快速測試');
  
  const quickSuites = ['form-submission', 'payuni-integration'];
  let allPassed = true;

  for (const suiteName of quickSuites) {
    const suite = TEST_CONFIG.suites[suiteName];
    printSection(`快速測試: ${suite.name}`);
    
    const success = runJest(suite.pattern, { verbose: false });
    
    if (!success) {
      allPassed = false;
    }
  }

  return allPassed;
}

// 主函數
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'suite':
      const suiteName = args[1];
      if (!suiteName) {
        printError('請指定測試套件名稱');
        printInfo('使用方式: npm run test:api suite <suite-name>');
        process.exit(1);
      }
      const success = runTestSuite(suiteName);
      process.exit(success ? 0 : 1);
      break;

    case 'coverage':
      const coverageSuccess = runCoverageTest();
      process.exit(coverageSuccess ? 0 : 1);
      break;

    case 'quick':
      const quickSuccess = runQuickTest();
      process.exit(quickSuccess ? 0 : 1);
      break;

    case 'all':
    default:
      const allSuccess = runAllTestSuites();
      process.exit(allSuccess ? 0 : 1);
      break;
  }
}

// 錯誤處理
process.on('uncaughtException', (error) => {
  printError(`未捕獲的異常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  printError(`未處理的 Promise 拒絕: ${reason}`);
  process.exit(1);
});

// 執行主函數
if (require.main === module) {
  main();
}

module.exports = {
  runTestSuite,
  runAllTestSuites,
  runCoverageTest,
  runQuickTest
};
