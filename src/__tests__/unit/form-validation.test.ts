import { validateFormData, validateEmail, validatePhone } from '@/lib/form-validation';
import { validFormData, invalidFormData, internationalPhoneFormData } from '../fixtures/form-data';

describe('表單驗證函數', () => {
  describe('validateEmail', () => {
    test('應該接受有效的 email 格式', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    test('應該拒絕無效的 email 格式', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        '<EMAIL>',
        'user@.com',
        ''
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validatePhone', () => {
    test('應該接受台灣本地電話號碼格式', () => {
      const validPhones = [
        '0912345678',
        '0987654321',
        '0223456789',
        '0712345678'
      ];

      validPhones.forEach(phone => {
        expect(validatePhone(phone)).toBe(true);
      });
    });

    test('應該接受國際電話號碼格式', () => {
      const validInternationalPhones = [
        '+886912345678',
        '+886987654321',
        '+886223456789'
      ];

      validInternationalPhones.forEach(phone => {
        expect(validatePhone(phone)).toBe(true);
      });
    });

    test('應該拒絕無效的電話號碼格式', () => {
      const invalidPhones = [
        '123',
        '12345',
        '091234567',
        '09123456789',
        'abc123',
        '+123456',
        ''
      ];

      invalidPhones.forEach(phone => {
        expect(validatePhone(phone)).toBe(false);
      });
    });
  });

  describe('validateFormData', () => {
    test('應該通過有效的表單資料驗證', () => {
      const result = validateFormData(validFormData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual({});
    });

    test('應該檢測必填欄位缺失', () => {
      const result = validateFormData(invalidFormData);
      expect(result.isValid).toBe(false);
      expect(result.errors.name).toBeDefined();
      expect(result.errors.email).toBeDefined();
      expect(result.errors.phone).toBeDefined();
      expect(result.errors.sessionTimes).toBeDefined();
    });

    test('應該接受國際電話號碼格式', () => {
      const result = validateFormData(internationalPhoneFormData);
      expect(result.isValid).toBe(true);
      expect(result.errors.phone).toBeUndefined();
    });

    test('應該驗證場次選擇', () => {
      const dataWithoutSession = {
        ...validFormData,
        sessionTimes: []
      };
      
      const result = validateFormData(dataWithoutSession);
      expect(result.isValid).toBe(false);
      expect(result.errors.sessionTimes).toBeDefined();
    });

    test('應該驗證參與類型', () => {
      const dataWithoutParticipationType = {
        ...validFormData,
        participationType: ''
      };
      
      const result = validateFormData(dataWithoutParticipationType);
      expect(result.isValid).toBe(false);
      expect(result.errors.participationType).toBeDefined();
    });
  });
});
