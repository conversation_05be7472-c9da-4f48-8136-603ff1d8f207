/**
 * Payment Callback API 路由測試
 * 測試 /api/payment/callback 端點的完整功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { POST, GET } from '@/app/api/payment/callback/route';

// Mock NextResponse.redirect to capture redirect URLs
let mockRedirectUrl: string | null = null;
const originalRedirect = NextResponse.redirect;

jest.mock('next/server', () => {
  const actual = jest.requireActual('next/server');
  return {
    ...actual,
    NextResponse: {
      ...actual.NextResponse,
      redirect: jest.fn((url: string, status?: number) => {
        mockRedirectUrl = url;
        // 創建一個模擬的 Response 對象
        const response = new Response(null, {
          status: status || 302,
          headers: { 'Location': url }
        });
        return response;
      })
    }
  };
});

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/payment/callback', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockRedirectUrl = null;

    // Mock environment variables
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';
  });

  describe('POST 回調處理', () => {
    test('應該正確處理 PayUni POST 回調並重定向', async () => {
      const formData = new FormData();
      formData.append('Status', 'SUCCESS');
      formData.append('MerOrderNo', 'pangea_1234567890');
      formData.append('TradeNo', 'TN123456789');
      formData.append('TradeAmt', '1500');
      formData.append('TradeStatus', '1');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302); // 重定向狀態碼

      // 檢查 mock 捕獲的重定向 URL
      expect(mockRedirectUrl).toContain('/payment/result');
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      expect(mockRedirectUrl).toContain('MerOrderNo=pangea_1234567890');
      expect(mockRedirectUrl).toContain('TradeNo=TN123456789');
      expect(mockRedirectUrl).toContain('TradeAmt=1500');
      expect(mockRedirectUrl).toContain('TradeStatus=1');
    });

    test('應該正確傳遞所有 POST 表單參數', async () => {
      const formData = new FormData();
      formData.append('Status', 'SUCCESS');
      formData.append('MerOrderNo', 'pangea_1234567890');
      formData.append('TradeNo', 'TN123456789');
      formData.append('TradeAmt', '1500');
      formData.append('TradeStatus', '1');
      formData.append('PaymentType', '1');
      formData.append('TradeDate', '2024-07-20');
      formData.append('TradeTime', '14:30:25');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      expect(mockRedirectUrl).toContain('MerOrderNo=pangea_1234567890');
      expect(mockRedirectUrl).toContain('TradeNo=TN123456789');
      expect(mockRedirectUrl).toContain('TradeAmt=1500');
      expect(mockRedirectUrl).toContain('TradeStatus=1');
      expect(mockRedirectUrl).toContain('PaymentType=1');
      expect(mockRedirectUrl).toContain('TradeDate=2024-07-20');
      expect(mockRedirectUrl).toContain('TradeTime=14%3A30%3A25'); // URL encoded
    });

    test('應該處理 POST 回調錯誤並重定向到失敗頁面', async () => {
      // 模擬無效的請求 - 傳入 null body
      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: null,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('/payment/result');
      expect(mockRedirectUrl).toContain('Status=FAILED');
      expect(mockRedirectUrl).toContain('Error=CallbackError');
    });

    test('應該處理空的表單資料', async () => {
      const formData = new FormData();

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('/payment/result');
    });

    test('應該處理包含特殊字符的參數', async () => {
      const formData = new FormData();
      formData.append('Status', 'SUCCESS');
      formData.append('MerOrderNo', 'pangea_test&special=chars');
      formData.append('Message', '付款成功！感謝您的購買');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      // 檢查特殊字符是否正確編碼
      expect(mockRedirectUrl).toContain('MerOrderNo=pangea_test%26special%3Dchars');
    });
  });

  describe('GET 回調處理', () => {
    test('應該正確處理 PayUni GET 回調並重定向', async () => {
      const url = 'http://localhost:3000/api/payment/callback?Status=SUCCESS&MerOrderNo=pangea_1234567890&TradeNo=TN123456789&TradeAmt=1500&TradeStatus=1';
      const request = new NextRequest(url, { method: 'GET' });

      const response = await GET(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('/payment/result');
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      expect(mockRedirectUrl).toContain('MerOrderNo=pangea_1234567890');
    });

    test('應該正確傳遞所有 GET 查詢參數', async () => {
      const queryParams = new URLSearchParams({
        Status: 'SUCCESS',
        MerOrderNo: 'pangea_1234567890',
        TradeNo: 'TN123456789',
        TradeAmt: '1500',
        TradeStatus: '1',
        PaymentType: '1',
        TradeDate: '2024-07-20',
        TradeTime: '14:30:25',
      });

      const url = `http://localhost:3000/api/payment/callback?${queryParams.toString()}`;
      const request = new NextRequest(url, { method: 'GET' });

      const response = await GET(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      expect(mockRedirectUrl).toContain('MerOrderNo=pangea_1234567890');
      expect(mockRedirectUrl).toContain('TradeNo=TN123456789');
      expect(mockRedirectUrl).toContain('TradeAmt=1500');
      expect(mockRedirectUrl).toContain('TradeStatus=1');
      expect(mockRedirectUrl).toContain('PaymentType=1');
      expect(mockRedirectUrl).toContain('TradeDate=2024-07-20');
      expect(mockRedirectUrl).toContain('TradeTime=14%3A30%3A25');
    });

    test('應該正確處理空的查詢參數', async () => {
      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'GET',
      });

      const response = await GET(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('/payment/result');
    });

    test('應該處理包含中文的查詢參數', async () => {
      const queryParams = new URLSearchParams({
        Status: 'SUCCESS',
        Message: '付款成功',
        ItemName: '錶匠體驗機芯拆解',
      });

      const url = `http://localhost:3000/api/payment/callback?${queryParams.toString()}`;
      const request = new NextRequest(url, { method: 'GET' });

      const response = await GET(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      expect(mockRedirectUrl).toContain('Message=%E4%BB%98%E6%AC%BE%E6%88%90%E5%8A%9F'); // URL encoded 中文
    });

    test('應該處理 GET 回調錯誤並重定向到失敗頁面', async () => {
      // 模擬 API 內部錯誤 - 通過 mock URLSearchParams 來觸發錯誤
      const originalURLSearchParams = global.URLSearchParams;
      global.URLSearchParams = class {
        constructor() {
          throw new Error('URLSearchParams error');
        }
      } as any;

      const request = new NextRequest('http://localhost:3000/api/payment/callback?Status=SUCCESS', {
        method: 'GET',
      });

      try {
        const response = await GET(request);

        expect(response.status).toBe(302);
        expect(mockRedirectUrl).toContain('/payment/result');
        expect(mockRedirectUrl).toContain('Status=FAILED');
        expect(mockRedirectUrl).toContain('Error=CallbackError');
      } finally {
        // 恢復原始函數
        global.URLSearchParams = originalURLSearchParams;
      }
    });
  });

  describe('重定向 URL 構建', () => {
    test('應該使用正確的基礎 URL', async () => {
      process.env.NEXT_PUBLIC_BASE_URL = 'https://example.com';

      const formData = new FormData();
      formData.append('Status', 'SUCCESS');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toMatch(/^https:\/\/example\.com\/payment\/result/);
    });

    test('應該正確處理長參數值', async () => {
      const formData = new FormData();
      formData.append('Status', 'SUCCESS');
      formData.append('LongParam', 'a'.repeat(1000)); // 1000 字符的長參數

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('/payment/result');
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
    });

    test('應該正確處理多個相同名稱的參數', async () => {
      const formData = new FormData();
      formData.append('Status', 'SUCCESS');
      formData.append('Item', 'item1');
      formData.append('Item', 'item2');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toContain('Status=SUCCESS');
      // FormData.append 會保留多個相同名稱的參數，但 URLSearchParams 可能只保留最後一個
      // 檢查至少包含其中一個 Item 參數
      expect(mockRedirectUrl).toMatch(/Item=item[12]/);
    });
  });

  describe('錯誤恢復', () => {
    test('應該在環境變數缺失時使用預設值', async () => {
      delete process.env.NEXT_PUBLIC_BASE_URL;

      const formData = new FormData();
      formData.append('Status', 'SUCCESS');

      const request = new NextRequest('http://localhost:3000/api/payment/callback', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);

      expect(response.status).toBe(302);
      expect(mockRedirectUrl).toMatch(/^http:\/\/localhost:3000\/payment\/result/);
    });
  });
});
