/**
 * Create Payment API 路由測試
 * 測試 /api/create-payment 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/create-payment/route';

// Mock dependencies
jest.mock('@/lib/payuni', () => ({
  createPaymentRequest: jest.fn(),
  calculateATMExpireDate: jest.fn(),
}));

jest.mock('@/config/environment-config', () => ({
  PAYUNI_CONFIG: {
    getMerchantId: jest.fn(() => 'S01421169'),
    getApiUrl: jest.fn(() => 'https://sandbox-api.payuni.com.tw/api/upp'),
    getHashKey: jest.fn(() => 'test_hash_key'),
    getHashIV: jest.fn(() => 'test_hash_iv'),
  },
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/create-payment', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock environment variables
    process.env.APP_ENVIRONMENT = 'sandbox';
    process.env.PAYUNI_NOTIFY_URL = 'https://example.com/webhook';
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';

    // Setup mock return values
    const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
    createPaymentRequest.mockReturnValue(mockPaymentResponse);
    calculateATMExpireDate.mockReturnValue('2025-07-12');
  });

  const validPaymentData = {
    orderNo: 'pangea_1234567890',
    eventName: '錶匠體驗機芯拆解',
    eventPrice: 1500,
    userName: '測試用戶',
    userEmail: '<EMAIL>',
  };

  const mockPaymentResponse = {
    MerID: 'S01421169',
    Version: '1.0',
    EncryptInfo: 'encrypted_data_here',
    HashInfo: 'hash_info_here',
    paymentUrl: 'https://sandbox-api.payuni.com.tw/api/payment',
  };

  describe('成功案例', () => {
    test('應該成功創建付款請求', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.MerID).toBe('S01421169');
      expect(data.EncryptInfo).toBe('encrypted_data_here');
      expect(data.HashInfo).toBe('hash_info_here');
      expect(data.paymentUrl).toBe('https://sandbox-api.payuni.com.tw/api/payment');
      
      expect(createPaymentRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          MerID: 'S01421169',
          MerTradeNo: 'pangea_1234567890',
          TradeAmt: 1500,
          ProdDesc: '錶匠體驗機芯拆解',
          UsrMail: '<EMAIL>',
          PaymentType: 'credit,atm',
          CreditType: '1',
          ExpireDate: '2024-07-22',
        })
      );
    });

    test('應該正確設定付款方式為信用卡和ATM', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const callArgs = createPaymentRequest.mock.calls[0][0];
      expect(callArgs.PaymentType).toBe('credit,atm');
      expect(callArgs.CreditType).toBe('1'); // 一次付清
    });

    test('應該正確設定回調 URL', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const callArgs = createPaymentRequest.mock.calls[0][0];
      expect(callArgs.NotifyURL).toBe('https://example.com/webhook');
      expect(callArgs.ReturnURL).toBe('http://localhost:3000/api/payment/callback');
      expect(callArgs.BackURL).toBe('http://localhost:3000/api/payment/callback');
    });

    test('應該正確計算 ATM 到期日', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-25');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      expect(calculateATMExpireDate).toHaveBeenCalled();
      const callArgs = createPaymentRequest.mock.calls[0][0];
      expect(callArgs.ExpireDate).toBe('2024-07-25');
    });

    test('應該包含正確的時間戳記', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const beforeTimestamp = Math.floor(Date.now() / 1000);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const afterTimestamp = Math.floor(Date.now() / 1000);
      const callArgs = createPaymentRequest.mock.calls[0][0];
      
      expect(callArgs.Timestamp).toBeGreaterThanOrEqual(beforeTimestamp);
      expect(callArgs.Timestamp).toBeLessThanOrEqual(afterTimestamp);
    });
  });

  describe('驗證錯誤', () => {
    test('應該拒絕缺少訂單號碼的請求', async () => {
      const incompleteData = {
        ...validPaymentData,
        orderNo: '',
      };

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });

    test('應該拒絕缺少活動名稱的請求', async () => {
      const incompleteData = {
        ...validPaymentData,
        eventName: '',
      };

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });

    test('應該拒絕缺少價格的請求', async () => {
      const incompleteData = {
        ...validPaymentData,
        eventPrice: 0,
      };

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });

    test('應該拒絕缺少用戶姓名的請求', async () => {
      const incompleteData = {
        ...validPaymentData,
        userName: '',
      };

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });

    test('應該拒絕缺少用戶郵箱的請求', async () => {
      const incompleteData = {
        ...validPaymentData,
        userEmail: '',
      };

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });

    test('應該拒絕所有欄位都缺少的請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必要欄位');
    });
  });

  describe('錯誤處理', () => {
    test('應該處理 PayUni 創建付款請求失敗', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockImplementation(() => {
        throw new Error('PayUni API 錯誤');
      });

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('建立付款請求失敗');
    });

    test('應該處理 ATM 到期日計算失敗', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockImplementation(() => {
        throw new Error('日期計算錯誤');
      });

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('建立付款請求失敗');
    });

    test('應該處理無效的 JSON 請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('建立付款請求失敗');
    });
  });

  describe('環境配置', () => {
    test('應該使用正確的商店 ID', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      const { PAYUNI_CONFIG } = require('@/config/environment-config');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      expect(PAYUNI_CONFIG.getMerchantId).toHaveBeenCalled();
      const callArgs = createPaymentRequest.mock.calls[0][0];
      expect(callArgs.MerID).toBe('S01421169');
    });

    test('應該使用正確的語系設定', async () => {
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/create-payment', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const callArgs = createPaymentRequest.mock.calls[0][0];
      expect(callArgs.Lang).toBe('zh-tw');
    });
  });
});
