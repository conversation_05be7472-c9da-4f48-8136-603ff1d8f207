/**
 * 付款流程整合測試
 * 測試 PayUni 相關函數的整合
 */

import {
  createPaymentRequest,
  convertTradeStatus,
  convertPaymentType,
  calculateATMExpireDate
} from '@/lib/payuni';
import { payuniTestData } from '../fixtures/form-data';

describe('付款流程整合測試', () => {
  beforeEach(() => {
    // Mock environment variables
    process.env.APP_ENVIRONMENT = 'sandbox';
    process.env.PAYUNI_SANDBOX_MERCHANT_ID = 'S01421169';
    process.env.PAYUNI_SANDBOX_HASH_KEY = 'test_hash_key';
    process.env.PAYUNI_SANDBOX_HASH_IV = 'test_hash_iv';
  });

  describe('PayUni 付款整合', () => {
    test('應該成功創建信用卡付款請求', () => {
      const tradeData = {
        MerTradeNo: payuniTestData.orderNo,
        TradeAmt: payuniTestData.amount,
        ItemName: payuniTestData.itemName,
        Email: payuniTestData.email,
        PaymentType: '1' // 信用卡
      };

      try {
        const result = createPaymentRequest(tradeData);
        expect(result).toHaveProperty('MerID');
        expect(result).toHaveProperty('EncryptInfo');
        expect(result).toHaveProperty('HashInfo');
      } catch (error) {
        // 如果環境變數未設定，跳過此測試
        expect(error.message).toContain('PayUni 商店 ID 未設定');
      }
    });

    test('應該正確轉換付款狀態', () => {
      expect(convertTradeStatus('1')).toBe('已付款');
      expect(convertTradeStatus('2')).toBe('付款失敗');
      expect(convertTradeStatus('9')).toBe('未付款');
    });

    test('應該正確轉換付款方式', () => {
      expect(convertPaymentType('1')).toBe('信用卡');
      expect(convertPaymentType('2')).toBe('ATM轉帳');
    });

    test('應該正確計算 ATM 到期日', () => {
      const expireDate = calculateATMExpireDate();

      expect(typeof expireDate).toBe('string');
      expect(expireDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });

  });
});
