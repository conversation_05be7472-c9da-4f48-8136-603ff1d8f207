/**
 * Google Sheets 整合測試
 * 測試 Google Sheets 資料操作功能
 */

// 暫時移除 MSW 依賴，專注於可測試的功能
import {
  readFromSheet
} from '@/lib/google-sheets';
import { sheetsTestData } from '../fixtures/form-data';

describe('Google Sheets 整合測試', () => {
  beforeEach(() => {
    // Mock environment variables
    process.env.GOOGLE_SHEETS_PRIVATE_KEY = 'test_private_key';
    process.env.GOOGLE_SHEETS_CLIENT_EMAIL = '<EMAIL>';
    process.env.GOOGLE_SHEETS_SPREADSHEET_ID = 'test_sheet_id';
  });

  describe('資料格式驗證', () => {
    test('應該正確驗證 Google Sheets 資料格式', () => {
      const registrationRow = sheetsTestData.registrationRow;

      // 檢查必要欄位
      expect(registrationRow[0]).toBeTruthy(); // 姓名
      expect(registrationRow[1]).toBeTruthy(); // Email
      expect(registrationRow[2]).toBeTruthy(); // 電話
      expect(registrationRow[3]).toBeTruthy(); // 場次
      expect(registrationRow[4]).toBeTruthy(); // 參與類型
      expect(registrationRow[6]).toBeTruthy(); // 訂單編號
    });

    test('應該正確驗證場次可用性資料格式', () => {
      const sessionData = sheetsTestData.sessionAvailability;

      // 檢查標題行
      expect(sessionData[0]).toEqual(['場次', '已報名人數', '狀態']);

      // 檢查資料行格式
      sessionData.slice(1).forEach(row => {
        expect(row.length).toBe(3);
        expect(row[0]).toBeTruthy(); // 場次名稱
        expect(row[1]).toMatch(/^\d+$/); // 已報名人數應該是數字
        expect(row[2]).toMatch(/^[123]$/); // 狀態應該是 1, 2, 或 3
      });
    });

    test('應該正確處理 UTM 參數資料', () => {
      const registrationRow = sheetsTestData.registrationRow;

      // 檢查 UTM 參數欄位
      expect(registrationRow[9]).toBe('facebook'); // UTM Source
      expect(registrationRow[10]).toBe('cpc'); // UTM Medium
      expect(registrationRow[11]).toBe('watch-experience'); // UTM Campaign
      expect(registrationRow[12]).toBeTruthy(); // FBP
      expect(registrationRow[13]).toBeTruthy(); // FBC
    });
  });

  describe('環境變數檢查', () => {
    test('應該檢查必要的環境變數', () => {
      const requiredEnvVars = [
        'GOOGLE_SHEETS_PRIVATE_KEY',
        'GOOGLE_SHEETS_CLIENT_EMAIL',
        'GOOGLE_SHEETS_SPREADSHEET_ID'
      ];

      requiredEnvVars.forEach(envVar => {
        expect(process.env[envVar]).toBeDefined();
      });
    });

    test('應該正確處理缺少環境變數的情況', async () => {
      // 暫時移除環境變數
      const originalPrivateKey = process.env.GOOGLE_SHEETS_PRIVATE_KEY;
      delete process.env.GOOGLE_SHEETS_PRIVATE_KEY;

      try {
        await readFromSheet('工作表1', 'A1:AD1000');
        fail('應該拋出錯誤');
      } catch (error) {
        expect(error).toBeDefined();
      } finally {
        // 恢復環境變數
        process.env.GOOGLE_SHEETS_PRIVATE_KEY = originalPrivateKey;
      }
    });
  });
});
