/**
 * 訂單查詢整合測試
 * 測試訂單狀態相關函數
 */

import { getOverallPaymentStatus } from '@/lib/payuni';

describe('訂單查詢整合測試', () => {

  describe('付款狀態判斷', () => {
    test('應該正確判斷已付款狀態', () => {
      const paidData = {
        TradeStatus: '1',
        RefundStatus: '0',
        RefundAmt: '0'
      };

      expect(getOverallPaymentStatus(paidData)).toBe('已付款');
    });

    test('應該正確判斷退款狀態', () => {
      const refundData = {
        TradeStatus: '1',
        RefundStatus: '2',
        RefundAmt: '3000',
        TradeAmt: '3000'
      };

      expect(getOverallPaymentStatus(refundData)).toBe('已退款');
    });

    test('應該正確判斷退款處理中狀態', () => {
      const processingRefundData = {
        TradeStatus: '1',
        RefundStatus: '1',
        RefundAmt: '0'
      };

      expect(getOverallPaymentStatus(processingRefundData)).toBe('退款處理中');
    });

  });
});
