/**
 * 表單提交整合測試
 * 測試表單驗證邏輯和資料處理
 */

import { validateFormData, sanitizeFormData, checkDuplicateSubmission } from '@/lib/form-validation';
import { validFormData } from '../fixtures/form-data';

describe('表單提交整合測試', () => {
  describe('表單資料處理流程', () => {
    test('應該完成完整的表單驗證和清理流程', () => {
      // 1. 清理表單資料
      const cleanedData = sanitizeFormData(validFormData);

      // 2. 驗證清理後的資料
      const validationResult = validateFormData(cleanedData);

      // 3. 驗證結果
      expect(validationResult.isValid).toBe(true);
      expect(validationResult.errors).toEqual({});
      expect(cleanedData.email).toBe(validFormData.email.toLowerCase());
      expect(cleanedData.name).toBe(validFormData.name.trim());
    });

    test('應該檢測無效的表單資料', () => {
      const invalidData = {
        name: '',
        email: 'invalid-email',
        phone: '123',
        sessionTimes: [],
        participationType: ''
      };

      const validationResult = validateFormData(invalidData);

      expect(validationResult.isValid).toBe(false);
      expect(validationResult.errors.name).toBeDefined();
      expect(validationResult.errors.email).toBeDefined();
      expect(validationResult.errors.phone).toBeDefined();
      expect(validationResult.errors.sessionTimes).toBeDefined();
      expect(validationResult.errors.participationType).toBeDefined();
    });

    test('應該處理包含 UTM 參數的表單資料', () => {
      const dataWithUTM = {
        ...validFormData,
        utmSource: 'facebook',
        utmMedium: 'cpc',
        utmCampaign: 'watch-experience-2025',
        fbp: 'fb.1.1234567890.1234567890',
        fbc: 'fb.1.1234567890.1234567890'
      };

      const cleanedData = sanitizeFormData(dataWithUTM);
      const validationResult = validateFormData(cleanedData);

      expect(validationResult.isValid).toBe(true);
      expect(cleanedData.utmSource).toBe('facebook');
      expect(cleanedData.fbp).toBe('fb.1.1234567890.1234567890');
    });

    test('應該檢測重複提交', () => {
      const email = '<EMAIL>';
      const recentTime = new Date().toISOString();
      const oldTime = new Date(Date.now() - 10 * 60 * 1000).toISOString(); // 10分鐘前

      // 測試最近提交（5分鐘內）
      expect(checkDuplicateSubmission(email, recentTime, 5)).toBe(true);

      // 測試較早提交（超過5分鐘）
      expect(checkDuplicateSubmission(email, oldTime, 5)).toBe(false);

      // 測試無提交記錄
      expect(checkDuplicateSubmission(email, undefined, 5)).toBe(false);
    });

    test('應該支援國際電話號碼格式', () => {
      const dataWithIntlPhone = {
        ...validFormData,
        phone: '+886912345678'
      };

      const validationResult = validateFormData(dataWithIntlPhone);
      expect(validationResult.isValid).toBe(true);
      expect(validationResult.errors.phone).toBeUndefined();
    });
  });
});
