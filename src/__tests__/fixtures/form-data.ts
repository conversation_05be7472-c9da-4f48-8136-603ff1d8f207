// 表單測試資料
export const validFormData = {
  name: '張測試',
  email: '<EMAIL>',
  phone: '0912345678',
  sessionTimes: ['台北 07/20（日）13:20'],
  participationType: 'individual',
  utmSource: 'facebook',
  utmMedium: 'cpc',
  utmCampaign: 'watch-experience',
  fbp: 'fb.1.1234567890.1234567890',
  fbc: 'fb.1.1234567890.1234567890'
};

export const invalidFormData = {
  name: '',
  email: 'invalid-email',
  phone: '123',
  sessionTimes: [],
  participationType: '',
};

export const internationalPhoneFormData = {
  ...validFormData,
  phone: '+886912345678'
};

export const duplicateSubmissionData = {
  ...validFormData,
  email: '<EMAIL>'
};

// PayUni 測試資料
export const payuniTestData = {
  orderNo: 'pangea_test_123',
  amount: 3000,
  itemName: '錶匠體驗機芯拆解',
  email: '<EMAIL>',
  paymentType: 'credit' as const
};

export const atmPaymentData = {
  ...payuniTestData,
  paymentType: 'atm' as const
};

// 訂單狀態測試資料
export const orderStatusTestData = {
  pending: {
    orderNo: 'pangea_pending_123',
    paymentStatus: '待付款',
    paymentMethod: '信用卡',
    tradeStatus: '9'
  },
  paid: {
    orderNo: 'pangea_paid_123',
    paymentStatus: '已付款',
    paymentMethod: '信用卡',
    tradeStatus: '1',
    paymentCompletedAt: '2025-01-01T12:00:00.000Z'
  },
  refunded: {
    orderNo: 'pangea_refunded_123',
    paymentStatus: '已退款',
    paymentMethod: '信用卡',
    tradeStatus: '1',
    refundStatus: '2',
    refundAmt: '3000'
  },
  failed: {
    orderNo: 'pangea_failed_123',
    paymentStatus: '付款失敗',
    paymentMethod: '信用卡',
    tradeStatus: '2'
  }
};

// 活動報名測試資料
export const eventRegistrationTestData = {
  valid: {
    sessionTimes: ['台北 07/20（日）13:20'],
    customTimeLocation: '',
    participationType: 'individual',
    name: '張測試',
    email: '<EMAIL>',
    phone: '0912345678',
    companionName: '',
    companionEmail: '',
    gender: 'male',
    age: '25-34',
    region: '台北市',
    watchTypes: ['機械錶'],
    watchBrands: 'Rolex, Omega',
    questions: '期待學習機芯拆解技術',
    agreeToTerms: true,
    utmSource: 'facebook',
    utmMedium: 'cpc',
    utmCampaign: 'watch-experience',
    fbp: 'fb.1.1234567890.1234567890',
    fbc: 'fb.1.1234567890.1234567890'
  },
  pairRegistration: {
    sessionTimes: ['台北 07/20（日）13:20'],
    customTimeLocation: '',
    participationType: 'pair',
    name: '張測試',
    email: '<EMAIL>',
    phone: '0912345678',
    companionName: '李同伴',
    companionEmail: '<EMAIL>',
    gender: 'male',
    age: '25-34',
    region: '台北市',
    watchTypes: ['機械錶'],
    watchBrands: 'Rolex',
    questions: '',
    agreeToTerms: true
  },
  customTime: {
    sessionTimes: ['有意願但無合適時間地點'],
    customTimeLocation: '希望在台中舉辦，週末時間',
    participationType: 'individual',
    name: '王測試',
    email: '<EMAIL>',
    phone: '0987654321',
    companionName: '',
    companionEmail: '',
    gender: 'female',
    age: '35-44',
    region: '台中市',
    watchTypes: ['石英錶'],
    watchBrands: 'Cartier',
    questions: '',
    agreeToTerms: true
  }
};

// 付款測試資料
export const paymentTestData = {
  valid: {
    orderNo: 'pangea_test_123',
    amount: 1500,
    itemName: '錶匠體驗機芯拆解',
    email: '<EMAIL>',
    paymentType: 'credit'
  },
  atm: {
    orderNo: 'pangea_atm_123',
    amount: 3000,
    itemName: '錶匠體驗機芯拆解 (雙人)',
    email: '<EMAIL>',
    paymentType: 'atm'
  }
};

// Google Sheets 測試資料
export const sheetsTestData = {
  registrationRow: [
    '張測試',
    '<EMAIL>',
    '0912345678',
    '台北 07/20（日）13:20',
    'individual',
    '2025-01-01T00:00:00.000Z',
    'pangea_test_123',
    '3000',
    '2', // AC 欄位 - 保留中
    'facebook',
    'cpc',
    'watch-experience',
    'fb.1.1234567890.1234567890',
    'fb.1.1234567890.1234567890'
  ],
  sessionAvailability: [
    ['場次', '已報名人數', '狀態'],
    ['台北 07/20（日）13:20', '5', '1'],
    ['台北 07/20（日）15:20', '8', '1'],
    ['台中 07/18（五）19:20', '3', '1']
  ]
};
