// PayUni 環境配置
// 可以輕鬆在測試環境和正式環境之間切換
// 注意：此檔案已被 src/config/environment-config.ts 取代，建議使用新的統一配置系統

import { getCurrentEnvironment } from './environment-config';

export const PAYUNI_CONFIG = {
  // 環境設定 - 使用統一的環境配置
  ENVIRONMENT: getCurrentEnvironment(),

  // API 端點
  API_ENDPOINTS: {
    sandbox: 'https://sandbox-api.payuni.com.tw/api/upp',
    production: 'https://api.payuni.com.tw/api/upp'
  },

  // 查詢 API 端點
  QUERY_API_ENDPOINTS: {
    sandbox: 'https://sandbox-api.payuni.com.tw/api/trade/query',
    production: 'https://api.payuni.com.tw/api/trade/query'
  },

  // 取得當前環境的 API 端點
  getApiUrl(): string {
    return this.API_ENDPOINTS[this.ENVIRONMENT as keyof typeof this.API_ENDPOINTS] || this.API_ENDPOINTS.production;
  },

  // 取得當前環境的查詢 API 端點
  getQueryApiUrl(): string {
    return this.QUERY_API_ENDPOINTS[this.ENVIRONMENT as keyof typeof this.QUERY_API_ENDPOINTS] || this.QUERY_API_ENDPOINTS.production;
  },

  // 取得當前環境的商店 ID
  getMerchantId(): string {
    const env = this.ENVIRONMENT;
    if (env === 'sandbox') {
      return process.env.PAYUNI_SANDBOX_MER_ID || '';
    } else {
      return process.env.PAYUNI_PRODUCTION_MER_ID || '';
    }
  },

  // 取得當前環境的 Hash Key
  getHashKey(): string {
    const env = this.ENVIRONMENT;
    if (env === 'sandbox') {
      return process.env.PAYUNI_SANDBOX_HASH_KEY || '';
    } else {
      return process.env.PAYUNI_PRODUCTION_HASH_KEY || '';
    }
  },

  // 取得當前環境的 Hash IV
  getHashIV(): string {
    const env = this.ENVIRONMENT;
    if (env === 'sandbox') {
      return process.env.PAYUNI_SANDBOX_HASH_IV || '';
    } else {
      return process.env.PAYUNI_PRODUCTION_HASH_IV || '';
    }
  },

  // 驗證環境配置
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const env = this.ENVIRONMENT;

    // 檢查基本環境變數
    if (!process.env.PAYUNI_NOTIFY_URL) {
      errors.push('PAYUNI_NOTIFY_URL 環境變數未設定');
    }

    if (!process.env.NEXT_PUBLIC_BASE_URL) {
      errors.push('NEXT_PUBLIC_BASE_URL 環境變數未設定');
    }

    // 檢查當前環境的憑證
    if (env === 'sandbox') {
      if (!process.env.PAYUNI_SANDBOX_MER_ID) {
        errors.push('PAYUNI_SANDBOX_MER_ID 環境變數未設定');
      }
      if (!process.env.PAYUNI_SANDBOX_HASH_KEY) {
        errors.push('PAYUNI_SANDBOX_HASH_KEY 環境變數未設定');
      }
      if (!process.env.PAYUNI_SANDBOX_HASH_IV) {
        errors.push('PAYUNI_SANDBOX_HASH_IV 環境變數未設定');
      }
    } else {
      if (!process.env.PAYUNI_PRODUCTION_MER_ID) {
        errors.push('PAYUNI_PRODUCTION_MER_ID 環境變數未設定');
      }
      if (!process.env.PAYUNI_PRODUCTION_HASH_KEY) {
        errors.push('PAYUNI_PRODUCTION_HASH_KEY 環境變數未設定');
      }
      if (!process.env.PAYUNI_PRODUCTION_HASH_IV) {
        errors.push('PAYUNI_PRODUCTION_HASH_IV 環境變數未設定');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

// 環境配置說明
export const PAYUNI_ENV_GUIDE = {
  sandbox: {
    description: '測試環境 - 用於開發和測試',
    apiUrl: 'https://sandbox-api.payuni.com.tw/api/upp',
    testCards: [
      '**************-0001 (一次付清)',
      '**************-0001 (一次付清)',
      '**************-0002 (模擬3D交易)',
      '**************-0002 (模擬3D交易)',
      '**************-0001 (分期付款)',
      '**************-0001 (分期付款)',
      '**************-0001 (銀聯卡)'
    ]
  },
  production: {
    description: '正式環境 - 用於實際交易',
    apiUrl: 'https://api.payuni.com.tw/api/upp',
    note: '請確保使用正式環境的商店憑證'
  }
};
