import { useState, useEffect } from 'react';

// 場次可用性介面
export interface SessionAvailability {
  sessionTime: string;
  maxCapacity: number;
  registeredCount: number;
  availableSpots: number;
  isAvailable: boolean;
  showAvailability: boolean; // 是否顯示剩餘名額（< 3 時才顯示）
}

// API 回應介面
interface SessionAvailabilityResponse {
  success: boolean;
  data: SessionAvailability[];
  cached?: boolean;
  cacheAge?: number;
  error?: string;
  timestamp?: number;
}

// Hook 回傳介面
interface UseSessionAvailabilityReturn {
  availability: SessionAvailability[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * 場次名額可用性 Hook
 * 用於查詢和管理活動場次的名額狀態
 */
export function useSessionAvailability(): UseSessionAvailabilityReturn {
  const [availability, setAvailability] = useState<SessionAvailability[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 查詢場次名額狀態...');
      
      const response = await fetch('/api/session-availability', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: SessionAvailabilityResponse = await response.json();

      if (result.success) {
        setAvailability(result.data);
        
        // 記錄快取狀態
        if (result.cached) {
          console.log(`💾 使用快取資料 (${result.cacheAge}秒前)`);
        } else {
          console.log('🆕 獲取最新場次名額資料');
        }
        
        // 記錄名額狀態
        const availabilityLog = result.data.map(session => ({
          場次: session.sessionTime,
          已報名: session.registeredCount,
          剩餘名額: session.availableSpots,
          是否可報名: session.isAvailable,
          顯示名額: session.showAvailability
        }));
        console.table(availabilityLog);
        
      } else {
        throw new Error(result.error || '查詢場次名額失敗');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知錯誤';
      console.error('❌ 查詢場次名額失敗:', errorMessage);
      setError(errorMessage);
      
      // 錯誤時設置空陣列，避免影響頁面顯示
      setAvailability([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始載入
  useEffect(() => {
    fetchAvailability();
  }, []);

  return {
    availability,
    loading,
    error,
    refetch: fetchAvailability
  };
}

/**
 * 根據場次時間獲取可用性資訊
 */
export function getSessionAvailabilityInfo(
  availability: SessionAvailability[],
  sessionTime: string
): SessionAvailability | null {
  return availability.find(item => item.sessionTime === sessionTime) || null;
}

/**
 * 檢查場次是否可用
 */
export function isSessionAvailable(
  availability: SessionAvailability[],
  sessionTime: string
): boolean {
  const info = getSessionAvailabilityInfo(availability, sessionTime);
  return info ? info.isAvailable : true; // 預設為可用
}

/**
 * 獲取場次剩餘名額文字
 */
export function getAvailabilityText(
  availability: SessionAvailability[],
  sessionTime: string
): string {
  const info = getSessionAvailabilityInfo(availability, sessionTime);
  
  if (!info) return '';
  
  if (!info.isAvailable) {
    return '已額滿';
  }
  
  if (info.showAvailability) {
    return `剩餘 ${info.availableSpots} 名額`;
  }
  
  return '';
}
