import { NextResponse } from 'next/server';
import { getSheetData } from '@/lib/google-sheets';

// 報名狀態常數（AC 欄位）
const REGISTRATION_STATUS = {
  CONFIRMED: 1,     // 已確認（計入名額）
  RESERVED: 2,      // 保留中（計入名額）
  CANCELLED: 3      // 已取消（不計入名額，釋放名額）
} as const;

// 付款狀態常數（W 欄位，保留給 PayUni 使用）
// const PAYMENT_STATUS = {
//   PAID: 1,        // 已付款
//   RESERVED: 2,    // 保留中（已報名未付款）
//   CANCELLED: 3    // 已取消
// } as const;

// 場次名額配置（與前端保持一致）
const SESSION_CAPACITY = {
  '台中 07/18（五）19:20': 8,
  '台北 07/19（六）13:20': 8,
  '台北 07/20（日）13:20': 8,
  '台北 07/20（日）15:20': 8,
  '台北 07/25（五）19:20': 8,
  '台北 07/26（六）13:20': 8,
  '台北 07/26（六）15:20': 8,
  '有意願但無合適時間地點': 999 // 無限制
} as const;

// 場次可用性介面
interface SessionAvailability {
  sessionTime: string;
  maxCapacity: number;
  registeredCount: number;
  availableSpots: number;
  isAvailable: boolean;
  showAvailability: boolean; // 是否顯示剩餘名額（< 3 時才顯示）
}

// 簡單的記憶體快取
interface CacheEntry {
  data: SessionAvailability[];
  timestamp: number;
}

let cache: CacheEntry | null = null;
const CACHE_DURATION = 30 * 1000; // 30秒快取

// 快取清除功能已移至 @/lib/session-availability-cache

/**
 * 計算場次報名統計
 * 優化邏輯：
 * 1. 使用 AC 欄位（報名狀態）進行名額計算
 *    - 狀態 1（已確認）和 2（保留中）計入名額
 *    - 狀態 3（已取消）不計入名額，釋放名額
 * 2. 只計算第一個場次（逗號分隔後的第一項）
 * 3. 雙人團報正確佔用 2 個名額
 */
function calculateSessionStats(registrationData: string[][]): Record<string, number> {
  const sessionStats: Record<string, number> = {};

  // 初始化所有場次統計
  Object.keys(SESSION_CAPACITY).forEach(session => {
    sessionStats[session] = 0;
  });

  // 遍歷報名資料（跳過標題行）
  for (let i = 1; i < registrationData.length; i++) {
    const row = registrationData[i];
    if (!row || row.length < 29) continue; // 確保資料完整（至少到 AC 欄位）

    const sessionTimes = row[1] || ''; // B欄位：場次時間
    const participationType = row[3] || ''; // D欄位：參加方式
    const registrationStatus = parseInt(row[28] || '0'); // AC欄位：報名狀態（第29個欄位，索引28）

    // 只計算已確認或保留中的報名（排除已取消）
    if (registrationStatus === REGISTRATION_STATUS.CONFIRMED || registrationStatus === REGISTRATION_STATUS.RESERVED) {
      // 只計算第一個場次（最終分配的場次會被移到最前面）
      const firstSession = sessionTimes.split(',')[0]?.trim();

      if (firstSession && SESSION_CAPACITY.hasOwnProperty(firstSession)) {
        // 雙人團報佔用 2 個名額
        const spotsUsed = participationType.includes('雙人') ? 2 : 1;
        sessionStats[firstSession] += spotsUsed;
      }
    }
  }

  return sessionStats;
}

/**
 * 取得場次可用性資訊
 */
async function getSessionAvailability(): Promise<SessionAvailability[]> {
  try {
    console.log('📊 查詢場次名額狀態...');

    // 效能優化：只查詢到 AC 欄位，但在處理時只使用必要欄位
    // B欄位（場次時間）、D欄位（參加方式）、AC欄位（報名狀態）
    const registrationData = await getSheetData('工作表1!A:AC');

    if (!registrationData || registrationData.length === 0) {
      console.log('⚠️ 未找到報名資料');
      return Object.keys(SESSION_CAPACITY).map(session => ({
        sessionTime: session,
        maxCapacity: SESSION_CAPACITY[session as keyof typeof SESSION_CAPACITY],
        registeredCount: 0,
        availableSpots: SESSION_CAPACITY[session as keyof typeof SESSION_CAPACITY],
        isAvailable: true,
        showAvailability: false
      }));
    }
    
    // 計算各場次報名統計
    const sessionStats = calculateSessionStats(registrationData);
    
    // 建立場次可用性資訊
    const availability: SessionAvailability[] = Object.keys(SESSION_CAPACITY).map(session => {
      const maxCapacity = SESSION_CAPACITY[session as keyof typeof SESSION_CAPACITY];
      const registeredCount = sessionStats[session] || 0;
      const availableSpots = Math.max(0, maxCapacity - registeredCount);
      const isAvailable = availableSpots > 0;
      const showAvailability = availableSpots < 3 && availableSpots > 0; // 剩餘名額少於3時才顯示
      
      return {
        sessionTime: session,
        maxCapacity,
        registeredCount,
        availableSpots,
        isAvailable,
        showAvailability
      };
    });
    
    console.log('✅ 場次名額統計完成:', availability.map(a => ({
      session: a.sessionTime,
      registered: a.registeredCount,
      available: a.availableSpots,
      show: a.showAvailability
    })));
    
    return availability;
    
  } catch (error) {
    console.error('❌ 查詢場次名額失敗:', error);
    throw error;
  }
}

export async function GET() {
  try {
    // 檢查快取
    const now = Date.now();
    if (cache && (now - cache.timestamp) < CACHE_DURATION) {
      console.log('💾 使用快取的場次名額資料');
      return NextResponse.json({
        success: true,
        data: cache.data,
        cached: true,
        cacheAge: Math.floor((now - cache.timestamp) / 1000)
      });
    }
    
    // 查詢最新資料
    const availability = await getSessionAvailability();
    
    // 更新快取
    cache = {
      data: availability,
      timestamp: now
    };
    
    return NextResponse.json({
      success: true,
      data: availability,
      cached: false,
      timestamp: now
    });
    
  } catch (error) {
    console.error('場次名額查詢 API 錯誤:', error);
    
    // 如果有快取資料，即使過期也返回
    if (cache) {
      console.log('⚠️ API 錯誤，返回快取資料');
      return NextResponse.json({
        success: true,
        data: cache.data,
        cached: true,
        error: 'API錯誤，顯示快取資料'
      });
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '查詢場次名額失敗',
      data: []
    }, { status: 500 });
  }
}
