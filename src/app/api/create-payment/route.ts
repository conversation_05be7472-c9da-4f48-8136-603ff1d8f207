import { NextResponse } from 'next/server';
import { createPaymentRequest, calculateATMExpireDate } from '@/lib/payuni';
import { PAYUNI_CONFIG } from '@/config/environment-config';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { orderNo, eventName, eventPrice, userName, userEmail } = body;

    // 驗證必要欄位
    if (!orderNo || !eventName || !eventPrice || !userName || !userEmail) {
      return NextResponse.json(
        { error: '缺少必要欄位' },
        { status: 400 }
      );
    }

    // Version 參數需要單獨傳送，不包含在加密資料中
    const version = '1.0';

    // 計算 ATM 轉帳動態到期日期（基於台灣時間 14:00 判斷）
    const expireDate = calculateATMExpireDate();

    // 建立 PayUni 付款請求所需的資料（根據官方文件規格）
    const tradeData = {
      MerID: PAYUNI_CONFIG.getMerchantId(),
      MerTradeNo: orderNo, // 商店訂單編號
      TradeAmt: eventPrice, // 訂單金額
      Timestamp: Math.floor(Date.now() / 1000), // 時間戳記
      ProdDesc: eventName, // 商品說明
      UsrMail: userEmail, // 消費者信箱
      Lang: 'zh-tw', // 語系
      NotifyURL: process.env.PAYUNI_NOTIFY_URL || '', // 背景通知網址
      ReturnURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 前景通知網址
      BackURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 返回商店按鈕網址
      // 付款方式設定：只開啟信用卡一次付清 & ATM 轉帳
      PaymentType: 'credit,atm', // credit=信用卡, atm=ATM轉帳
      CreditType: '1', // 1=一次付清
      ExpireDate: expireDate, // ATM 轉帳有效期限：當日+2天
    };

    // 產生加密後的付款參數
    const paymentRequest = createPaymentRequest(tradeData);

    return NextResponse.json({
      success: true,
      ...paymentRequest,
      Version: version,
      ApiUrl: PAYUNI_CONFIG.getApiUrl() // 返回正確的 PayUni API URL
    });

  } catch (error) {
    console.error('建立付款請求失敗:', error);
    return NextResponse.json(
      { error: '建立付款請求失敗' },
      { status: 500 }
    );
  }
}
