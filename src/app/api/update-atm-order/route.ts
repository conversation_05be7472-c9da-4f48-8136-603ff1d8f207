import { NextRequest, NextResponse } from 'next/server';
import { getSheetData, getSheetsClient } from '@/lib/google-sheets';
import { clearSessionAvailabilityCache } from '@/lib/session-availability-cache';

// 報名狀態常數（AC 欄位）
const REGISTRATION_STATUS = {
  CONFIRMED: 1,     // 已確認（計入名額）
  RESERVED: 2,      // 保留中（計入名額）
  CANCELLED: 3      // 已取消（不計入名額，釋放名額）
} as const;

/**
 * 取得 UTC+8 時區的時間字串（保留供未來使用）
 */
// function getUTC8TimeString(): string {
//   const now = new Date();
//   const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
//   return utc8Time.toISOString().replace('Z', '+08:00');
// }

/**
 * ATM 轉帳取號成功後立即更新 Google Sheets
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderNo, payuniTradeNo, paymentMethod, bankType, payNo, expireDate } = body;

    if (!orderNo) {
      return NextResponse.json(
        { error: '缺少訂單號碼' },
        { status: 400 }
      );
    }

    console.log(`🏧 ATM 取號成功，更新訂單: ${orderNo}`, {
      payuniTradeNo,
      paymentMethod,
      bankType,
      payNo,
      expireDate
    });

    // 更新 Google Sheets 中的訂單記錄
    await updateOrderPaymentStatus(orderNo, {
      paymentStatus: '待付款',
      payuniTradeNo: payuniTradeNo || '',
      paymentMethod: paymentMethod || 'ATM轉帳',
      paymentCompletedAt: '', // ATM 轉帳建立時不設定完成時間
    });

    // 清除場次名額快取，確保下次查詢時獲取最新資料
    clearSessionAvailabilityCache();
    console.log(`🗑️ 已清除場次名額快取`);

    return NextResponse.json({
      success: true,
      message: 'ATM 訂單狀態更新成功'
    });

  } catch (error) {
    console.error('❌ 更新 ATM 訂單狀態失敗:', error);
    return NextResponse.json(
      { error: '更新失敗' },
      { status: 500 }
    );
  }
}

/**
 * 更新 Google Sheets 中的訂單付款狀態
 */
async function updateOrderPaymentStatus(
  orderNo: string,
  paymentInfo: {
    paymentStatus: string;
    payuniTradeNo: string;
    paymentMethod: string;
    paymentCompletedAt: string;
  }
) {
  const sheetName = '工作表1';
  console.log(`📊 開始更新 Google Sheets，訂單: ${orderNo}，付款資訊:`, paymentInfo);

  // 1. 讀取所有訂單資料
  console.log(`📖 讀取 Google Sheets 資料: ${sheetName}!A:AD`);
  const sheetData = await getSheetData(`${sheetName}!A:AD`);

  if (!sheetData || sheetData.length === 0) {
    console.error('❌ Google Sheets 沒有資料');
    throw new Error('找不到訂單資料');
  }

  // 2. 找到對應的訂單行 (訂單號碼在 V 欄，索引 21)
  console.log(`🔍 尋找訂單: ${orderNo}`);
  let targetRowIndex = -1;
  for (let i = 0; i < sheetData.length; i++) {
    if (sheetData[i][21] === orderNo) { // V 欄位 (索引 21)
      targetRowIndex = i;
      break;
    }
  }

  if (targetRowIndex === -1) {
    console.error(`❌ 找不到訂單: ${orderNo}`);
    throw new Error(`找不到訂單: ${orderNo}`);
  }

  // 3. 計算實際的行號 (Google Sheets 從 1 開始計算，且要考慮標題行)
  const actualRowNumber = targetRowIndex + 1;
  console.log(`📍 找到訂單 ${orderNo} 在第 ${actualRowNumber} 行`);

  // 4. 更新 Google Sheets
  const sheets = getSheetsClient();

  try {
    // 更新 W 欄位 (付款狀態)
    console.log(`📝 更新 W${actualRowNumber} 欄位 (付款狀態): ${paymentInfo.paymentStatus}`);
    await sheets.spreadsheets.values.update({
      spreadsheetId: process.env.GOOGLE_SHEET_ID,
      range: `${sheetName}!W${actualRowNumber}`,
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: [[paymentInfo.paymentStatus]],
      },
    });
    console.log(`✅ W 欄位更新成功`);

    // 更新 Y:AA 欄位 (PayUni交易號、付款方式、付款完成時間)
    console.log(`📝 更新 Y${actualRowNumber}:AA${actualRowNumber} 欄位:`, {
      Y: paymentInfo.payuniTradeNo,
      Z: paymentInfo.paymentMethod,
      AA: paymentInfo.paymentCompletedAt
    });
    await sheets.spreadsheets.values.update({
      spreadsheetId: process.env.GOOGLE_SHEET_ID,
      range: `${sheetName}!Y${actualRowNumber}:AA${actualRowNumber}`,
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: [[
          paymentInfo.payuniTradeNo,      // Y: PayUni交易號
          paymentInfo.paymentMethod,      // Z: 付款方式
          paymentInfo.paymentCompletedAt, // AA: 付款完成時間
        ]],
      },
    });
    console.log(`✅ Y:AA 欄位更新成功`);

    // 更新 AC 欄位 (報名狀態) - ATM 取號成功時設為保留中
    const registrationStatus = REGISTRATION_STATUS.RESERVED; // ATM 取號成功 -> 保留中

    console.log(`📝 更新 AC${actualRowNumber} 欄位 (報名狀態): ${registrationStatus} (保留中)`);
    await sheets.spreadsheets.values.update({
      spreadsheetId: process.env.GOOGLE_SHEET_ID,
      range: `${sheetName}!AC${actualRowNumber}`,
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: [[registrationStatus]],
      },
    });
    console.log(`✅ AC 欄位更新成功`);

    console.log(`🎉 成功更新訂單 ${orderNo} 在第 ${actualRowNumber} 行，付款資訊:`, paymentInfo, `報名狀態: 保留中`);
  } catch (sheetsError) {
    console.error(`❌ Google Sheets API 更新失敗:`, sheetsError);
    throw sheetsError;
  }
}
