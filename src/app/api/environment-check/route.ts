import { NextResponse } from 'next/server';
import { 
  getCurrentEnvironment, 
  validateEnvironmentConfig, 
  getEnvironmentSummary,
  PAYUNI_CONFIG,
  GOOGLE_SHEETS_CONFIG,
  META_PIXEL_CONFIG,
  GTM_CONFIG
} from '@/config/environment-config';

/**
 * 環境配置檢查 API
 * 用於驗證環境變數配置是否正確
 * 
 * GET /api/environment-check
 */
export async function GET() {
  try {
    const currentEnv = getCurrentEnvironment();
    const validation = validateEnvironmentConfig();
    const summary = getEnvironmentSummary();

    // 詳細配置檢查
    const detailedConfig = {
      environment: currentEnv,
      payuni: {
        environment: PAYUNI_CONFIG.ENVIRONMENT,
        merchantId: PAYUNI_CONFIG.getMerchantId() ? '已設定' : '未設定',
        hashKey: PAYUNI_CONFIG.getHashKey() ? '已設定' : '未設定',
        hashIV: PAYUNI_CONFIG.getHashIV() ? '已設定' : '未設定',
        apiUrl: PAYUNI_CONFIG.getApiUrl(),
        queryUrl: PAYUNI_CONFIG.getQueryUrl()
      },
      googleSheets: {
        serviceAccountEmail: GOOGLE_SHEETS_CONFIG.getServiceAccountEmail() ? '已設定' : '未設定',
        privateKey: GOOGLE_SHEETS_CONFIG.getPrivateKey() ? '已設定' : '未設定',
        sheetId: GOOGLE_SHEETS_CONFIG.getSheetId() || '未設定',
        watchSheetId: GOOGLE_SHEETS_CONFIG.getWatchSheetId() || '未設定',
        blogSheetId: GOOGLE_SHEETS_CONFIG.getBlogSheetId() || '未設定'
      },
      metaPixel: {
        pixelId: META_PIXEL_CONFIG.getPixelId() || '未設定',
        accessToken: META_PIXEL_CONFIG.getAccessToken() ? '已設定' : '未設定',
        testEventCode: META_PIXEL_CONFIG.getTestEventCode() || '未設定',
        capiEnabled: process.env.NEXT_PUBLIC_CAPI_ENABLED === 'true'
      },
      gtm: {
        gtmId: GTM_CONFIG.getGTMId() || '未設定',
        proxyDomain: GTM_CONFIG.getProxyDomain() || '未設定'
      }
    };

    // 環境變數檢查
    const envVarsCheck = {
      // 新的環境變數
      APP_ENVIRONMENT: process.env.APP_ENVIRONMENT || '未設定',
      
      // PayUni 環境變數
      PAYUNI_SANDBOX_MER_ID: process.env.PAYUNI_SANDBOX_MER_ID ? '已設定' : '未設定',
      PAYUNI_PRODUCTION_MER_ID: process.env.PAYUNI_PRODUCTION_MER_ID ? '已設定' : '未設定',
      
      // Google Sheets 環境變數
      GOOGLE_SANDBOX_SHEET_ID: process.env.GOOGLE_SANDBOX_SHEET_ID ? '已設定' : '未設定',
      GOOGLE_PRODUCTION_SHEET_ID: process.env.GOOGLE_PRODUCTION_SHEET_ID ? '已設定' : '未設定',
      
      // Meta Pixel 環境變數
      META_SANDBOX_PIXEL_ID: process.env.META_SANDBOX_PIXEL_ID ? '已設定' : '未設定',
      META_PRODUCTION_PIXEL_ID: process.env.META_PRODUCTION_PIXEL_ID ? '已設定' : '未設定',
      
      // GTM 環境變數
      NEXT_PUBLIC_GTM_SANDBOX_ID: process.env.NEXT_PUBLIC_GTM_SANDBOX_ID ? '已設定' : '未設定',
      NEXT_PUBLIC_GTM_PRODUCTION_ID: process.env.NEXT_PUBLIC_GTM_PRODUCTION_ID ? '已設定' : '未設定',
      
      // 向後相容性檢查
      PAYUNI_ENVIRONMENT: process.env.PAYUNI_ENVIRONMENT || '未設定',
      GOOGLE_SHEET_ID: process.env.GOOGLE_SHEET_ID ? '已設定' : '未設定',
      META_PIXEL_ID: process.env.META_PIXEL_ID ? '已設定' : '未設定',
      NEXT_PUBLIC_GTM_ID: process.env.NEXT_PUBLIC_GTM_ID ? '已設定' : '未設定'
    };

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      currentEnvironment: currentEnv,
      validation: {
        isValid: validation.isValid,
        errors: validation.errors
      },
      summary,
      detailedConfig,
      environmentVariables: envVarsCheck,
      recommendations: generateRecommendations(validation, currentEnv)
    });

  } catch (error) {
    console.error('環境配置檢查失敗:', error);
    return NextResponse.json({
      success: false,
      error: '環境配置檢查失敗',
      details: error instanceof Error ? error.message : '未知錯誤'
    }, { status: 500 });
  }
}

/**
 * 生成配置建議
 */
function generateRecommendations(validation: { isValid: boolean; errors: string[] }, currentEnv: string) {
  const recommendations: string[] = [];

  if (!validation.isValid) {
    recommendations.push('❌ 環境配置驗證失敗，請檢查以下錯誤：');
    validation.errors.forEach(error => {
      recommendations.push(`   • ${error}`);
    });
  } else {
    recommendations.push('✅ 環境配置驗證通過');
  }

  // 環境特定建議
  if (currentEnv === 'sandbox') {
    recommendations.push('🧪 當前使用測試環境，適合開發和測試');
    recommendations.push('💡 切換到正式環境請設定 APP_ENVIRONMENT=production');
  } else {
    recommendations.push('🚀 當前使用正式環境，請確保所有配置正確');
    recommendations.push('⚠️ 請確認正式環境的憑證和設定都已正確配置');
  }

  // 向後相容性建議
  if (process.env.PAYUNI_ENVIRONMENT && !process.env.APP_ENVIRONMENT) {
    recommendations.push('🔄 建議設定 APP_ENVIRONMENT 取代 PAYUNI_ENVIRONMENT');
  }

  return recommendations;
}
