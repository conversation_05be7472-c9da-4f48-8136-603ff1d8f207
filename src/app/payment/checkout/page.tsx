'use client';

import { useState, useEffect, useRef, useCallback, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';

interface OrderData {
  orderNo: string;
  name: string;
  email: string;
  eventName: string;
  eventPrice: number;
  sessionTimes: string[];
  participationType: string;
  submittedAt: string;
}

interface PayUniPaymentData {
  MerID: string;
  EncryptInfo: string;
  HashInfo: string;
  Version: string;
  ApiUrl: string;
}

function CheckoutPageContent() {
  const searchParams = useSearchParams();
  const orderNo = searchParams.get('orderNo');
  
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [paymentData, setPaymentData] = useState<PayUniPaymentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const formRef = useRef<HTMLFormElement>(null);

  // 當 paymentData 準備好時，自動提交表單跳轉到 PayUni
  useEffect(() => {
    if (paymentData && formRef.current) {
      formRef.current.submit();
    }
  }, [paymentData]);

  const fetchOrderData = useCallback(async () => {
    try {
      const response = await fetch(`/api/order/${orderNo}`);
      if (!response.ok) {
        throw new Error('無法找到訂單資料');
      }
      const data = await response.json();
      setOrderData(data);
    } catch (error) {
      console.error('載入訂單資料失敗:', error);
      setError(error instanceof Error ? error.message : '載入訂單資料失敗');
    } finally {
      setIsLoading(false);
    }
  }, [orderNo]);

  // 載入訂單資料
  useEffect(() => {
    if (!orderNo) {
      setError('缺少訂單號碼');
      setIsLoading(false);
      return;
    }

    fetchOrderData();
  }, [orderNo, fetchOrderData]);

  const handlePayment = async () => {
    if (!orderData) return;

    setIsProcessing(true);
    setError(null);

    try {
      const response = await fetch('/api/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderNo: orderData.orderNo,
          eventName: orderData.eventName,
          eventPrice: orderData.eventPrice,
          userName: orderData.name,
          userEmail: orderData.email,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '建立付款失敗');
      }

      const paymentResult: PayUniPaymentData = await response.json();
      setPaymentData(paymentResult);

    } catch (error) {
      console.error('建立付款失敗:', error);
      setError(error instanceof Error ? error.message : '建立付款失敗');
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">載入訂單資料中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">發生錯誤</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.href = '/movement-assembling-booking'}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回報名頁面
          </button>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">找不到訂單資料</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 頁面標題 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">完成付款</h1>
          <p className="text-gray-600">請確認您的訂單資訊並完成付款</p>
        </div>

        {/* 訂單資訊卡片 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">訂單資訊</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">訂單號碼：</span>
              <span className="font-medium">{orderData.orderNo}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">活動名稱：</span>
              <span className="font-medium">{orderData.eventName}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">報名者：</span>
              <span className="font-medium">{orderData.name}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Email：</span>
              <span className="font-medium">{orderData.email}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">場次時間：</span>
              <span className="font-medium">{orderData.sessionTimes.join(', ')}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">參加方式：</span>
              <span className="font-medium">{orderData.participationType}</span>
            </div>
            
            <hr className="my-4" />
            
            <div className="flex justify-between text-lg font-semibold">
              <span>總金額：</span>
              <span className="text-blue-600">NT$ {orderData.eventPrice.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 付款按鈕 */}
        <div className="text-center">
          <button
            onClick={handlePayment}
            disabled={isProcessing}
            className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-all duration-200 ${
              isProcessing
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
            }`}
            style={!isProcessing ? { backgroundColor: '#2b354d' } : {}}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                處理中...
              </div>
            ) : (
              '前往付款'
            )}
          </button>
          
          <p className="text-sm text-gray-500 mt-4">
            點擊後將跳轉至 PayUni 安全付款頁面
          </p>
        </div>

        {/* 隱藏的 PayUni 表單 */}
        <form
          ref={formRef}
          action={paymentData?.ApiUrl || ''}
          method="POST"
          style={{ display: 'none' }}
        >
          <input type="text" name="MerID" value={paymentData?.MerID || ''} readOnly />
          <input type="text" name="EncryptInfo" value={paymentData?.EncryptInfo || ''} readOnly />
          <input type="text" name="HashInfo" value={paymentData?.HashInfo || ''} readOnly />
          <input type="text" name="Version" value={paymentData?.Version || ''} readOnly />
        </form>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">載入中...</div>}>
      <CheckoutPageContent />
    </Suspense>
  );
}
