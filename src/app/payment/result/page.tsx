'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';

// 銀行代碼對應表（根據 PayUni 文件）
const BANK_CODES: Record<string, string> = {
  '004': '台灣銀行',
  '005': '土地銀行',
  '006': '合作金庫',
  '007': '第一銀行',
  '008': '華南銀行',
  '009': '彰化銀行',
  '011': '上海銀行',
  '012': '台北富邦',
  '013': '國泰世華',
  '017': '兆豐銀行',
  '018': '農業金庫',
  '021': '花旗銀行',
  '050': '台灣企銀',
  '052': '渣打銀行',
  '053': '台中銀行',
  '054': '京城銀行',
  '081': '匯豐銀行',
  '103': '台新銀行',
  '108': '陽信銀行',
  '114': '中華郵政',
  '118': '板信銀行',
  '147': '三信銀行',
  '803': '聯邦銀行',
  '805': '遠東銀行',
  '806': '元大銀行',
  '807': '永豐銀行',
  '808': '玉山銀行',
  '809': '凱基銀行',
  '812': '台新銀行',
  '822': '中國信託',
};

// 格式化銀行資訊
const formatBankInfo = (bankType?: string): string => {
  if (!bankType) return '未知銀行';
  return BANK_CODES[bankType] || `銀行代碼 ${bankType}`;
};

// 格式化到期時間
const formatExpireDate = (expireDate?: string): string => {
  if (!expireDate) return '';
  try {
    // PayUni 格式: YYYY-MM-DD HH:II:SS
    const date = new Date(expireDate);
    return date.toLocaleString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Taipei'
    });
  } catch {
    return expireDate;
  }
};

interface PaymentData {
  Status?: string;
  Message?: string;
  MerID?: string;
  MerTradeNo?: string;
  TradeNo?: string;
  TradeAmt?: string;
  TradeStatus?: string;
  PaymentType?: string;
  // ATM 轉帳專用欄位
  BankType?: string;
  PayNo?: string;
  ExpireDate?: string;
  [key: string]: unknown;
}

function PaymentResultContent() {
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'failed' | 'pending'>('loading');
  const [orderNo, setOrderNo] = useState<string>('');
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [error, setError] = useState<string>('');

  // ATM 轉帳取號成功時立即更新 Google Sheets
  const updateATMOrderStatus = async (paymentData: PaymentData) => {
    try {
      console.log('🏧 ATM 取號成功，立即更新 Google Sheets:', paymentData);

      const response = await fetch('/api/update-atm-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderNo: paymentData.MerTradeNo,
          payuniTradeNo: paymentData.TradeNo || '',
          paymentMethod: 'ATM轉帳',
          bankType: paymentData.BankType,
          payNo: paymentData.PayNo,
          expireDate: paymentData.ExpireDate,
        }),
      });

      if (response.ok) {
        console.log('✅ ATM 訂單狀態更新成功');
      } else {
        console.error('❌ ATM 訂單狀態更新失敗');
      }
    } catch (error) {
      console.error('❌ 更新 ATM 訂單狀態時發生錯誤:', error);
    }
  };

  useEffect(() => {
    const processPaymentResult = async () => {
      try {
        // 獲取 PayUni 返回的加密參數
        const encryptInfo = searchParams.get('EncryptInfo');
        const hashInfo = searchParams.get('HashInfo');
        const payuniStatus = searchParams.get('Status');

        // 詳細記錄所有參數以便調試
        const allParams = Object.fromEntries(searchParams.entries());
        console.log('PayUni 返回參數 (原始):', {
          Status: payuniStatus,
          EncryptInfo: encryptInfo,
          HashInfo: hashInfo,
          allParams,
          url: window.location.href,
          search: window.location.search
        });

        // 如果有加密資料，進行解密
        if (encryptInfo && hashInfo) {
          try {
            const response = await fetch('/api/decrypt-payuni', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ encryptInfo }),
            });

            const result = await response.json();

            if (result.success && result.data) {
              const decryptedData = result.data as PaymentData;
              setPaymentData(decryptedData);

              console.log('PayUni 解密後資料:', decryptedData);

              // 設定訂單號碼
              if (decryptedData.MerTradeNo) {
                setOrderNo(decryptedData.MerTradeNo);
              }

              // 根據解密後的狀態設定頁面狀態
              // 特別處理 ATM 轉帳的狀態判斷
              if (decryptedData.Status === 'SUCCESS' && decryptedData.TradeStatus === '1') {
                // 真正的付款成功
                setStatus('success');
              } else if (
                decryptedData.Status === 'SUCCESS' &&
                decryptedData.TradeStatus === '0' &&
                decryptedData.PaymentType === '2'
              ) {
                // ATM 轉帳取號成功
                setStatus('pending');

                // 立即更新 Google Sheets
                if (decryptedData.PayNo && decryptedData.MerTradeNo) {
                  updateATMOrderStatus(decryptedData);
                }
              } else if (
                decryptedData.Status === 'UNAPPROVED' ||
                decryptedData.TradeStatus === '8'
              ) {
                // 其他待確認狀態
                setStatus('pending');
              } else {
                setStatus('failed');
              }
            } else {
              console.error('解密失敗:', result.error);
              setError(result.error || '解密失敗');
              setStatus('failed');
            }
          } catch (decryptError) {
            console.error('解密請求失敗:', decryptError);
            setError('解密請求失敗');
            setStatus('failed');
          }
        } else if (payuniStatus) {
          // 如果沒有加密資料但有直接的狀態參數（可能是測試環境或舊版本）
          console.log('使用直接狀態參數:', payuniStatus);

          // 檢查是否為 ATM 轉帳相關的狀態
          const paymentType = searchParams.get('PaymentType');
          const tradeStatus = searchParams.get('TradeStatus');

          if (payuniStatus === 'SUCCESS' && tradeStatus === '1') {
            setStatus('success');
          } else if (
            payuniStatus === 'SUCCESS' &&
            tradeStatus === '0' &&
            paymentType === '2'
          ) {
            // ATM 轉帳取號成功
            setStatus('pending');

            // 立即更新 Google Sheets
            const merOrderNo = searchParams.get('MerOrderNo') || searchParams.get('MerTradeNo');
            const tradeNo = searchParams.get('TradeNo');
            const bankType = searchParams.get('BankType');
            const payNo = searchParams.get('PayNo');
            const expireDate = searchParams.get('ExpireDate');

            if (payNo && merOrderNo) {
              const atmData: PaymentData = {
                MerTradeNo: merOrderNo,
                TradeNo: tradeNo || undefined,
                PaymentType: paymentType,
                BankType: bankType || undefined,
                PayNo: payNo,
                ExpireDate: expireDate || undefined
              };
              setPaymentData(atmData); // 設定 paymentData 以便顯示轉帳資訊
              updateATMOrderStatus(atmData);
            }
          } else if (
            payuniStatus === 'PENDING' ||
            payuniStatus === 'UNAPPROVED' ||
            tradeStatus === '8'
          ) {
            // 其他待確認狀態
            setStatus('pending');
          } else {
            setStatus('failed');
          }

          // 嘗試獲取訂單號碼
          const merOrderNo = searchParams.get('MerOrderNo') || searchParams.get('MerTradeNo');
          if (merOrderNo) {
            setOrderNo(merOrderNo);
          }
        } else {
          // 沒有任何有效的返回參數
          console.log('沒有找到有效的 PayUni 返回參數');
          setStatus('failed');
          setError('沒有收到有效的付款結果');
        }
      } catch (error) {
        console.error('處理付款結果時發生錯誤:', error);
        setError('處理付款結果時發生錯誤');
        setStatus('failed');
      }
    };

    processPaymentResult();
  }, [searchParams]);

  const handleBackToHome = () => {
    window.location.href = '/';
  };

  const handleCheckOrder = () => {
    window.location.href = '/order/status';
  };

  const handleRetryPayment = async () => {
    if (orderNo) {
      try {
        // 使用 API 驗證訂單並取得付款資料
        const response = await fetch('/api/retry-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ orderNo: orderNo }),
        });

        const result = await response.json();

        if (response.ok && result.paymentData) {
          // 建立隱藏表單並自動提交到 PayUni
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = result.paymentData.action;
          form.style.display = 'none';

          // 添加必要的 PayUni 參數
          const fields = {
            MerID: result.paymentData.MerID,
            EncryptInfo: result.paymentData.EncryptInfo,
            HashInfo: result.paymentData.HashInfo,
            Version: result.paymentData.Version
          };

          Object.entries(fields).forEach(([name, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = name;
            input.value = value;
            form.appendChild(input);
          });

          document.body.appendChild(form);
          form.submit();
        } else {
          // 失敗，顯示錯誤訊息
          alert(result.error || '無法重新付款');
        }
      } catch (error) {
        console.error('重新付款失敗:', error);
        alert('處理失敗，請稍後再試');
      }
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">處理付款結果中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-8">
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          
          {/* 付款成功 */}
          {status === 'success' && (
            <>
              <div className="text-green-500 text-6xl mb-4">✅</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">付款成功！</h1>
              <p className="text-gray-600 mb-6">
                感謝您的報名，我們已收到您的付款。
                <br />
                確認郵件將發送至您的信箱。
              </p>
              {orderNo && (
                <p className="text-sm text-gray-500 mb-6">
                  訂單號碼：{orderNo}
                </p>
              )}
              <div className="space-y-3">
                <button
                  onClick={handleCheckOrder}
                  className="w-full bg-[#2b354d] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#1e2a3a] transition-colors cursor-pointer"
                >
                  查看訂單詳情
                </button>
                <button
                  onClick={handleBackToHome}
                  className="w-full bg-white text-[#2b354d] border border-[#2b354d] py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  返回首頁
                </button>
              </div>
            </>
          )}

          {/* 付款失敗 */}
          {status === 'failed' && (
            <>
              <div className="text-red-500 text-6xl mb-4">❌</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">付款失敗</h1>
              <p className="text-gray-600 mb-6">
                很抱歉，您的付款未能成功完成。
                <br />
                請檢查您的付款資訊或稍後再試。
                {error && (
                  <>
                    <br />
                    <span className="text-red-600 text-sm">錯誤詳情：{error}</span>
                  </>
                )}
              </p>
              {orderNo && (
                <p className="text-sm text-gray-500 mb-6">
                  訂單號碼：{orderNo}
                </p>
              )}

              <div className="space-y-3">
                <button
                  onClick={handleCheckOrder}
                  className="w-full bg-[#2b354d] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#1e2a3a] transition-colors cursor-pointer"
                >
                  查看訂單狀態
                </button>
                {orderNo && (
                  <button
                    onClick={handleRetryPayment}
                    className="w-full bg-white text-[#2b354d] border border-[#2b354d] py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors cursor-pointer"
                  >
                    重新付款
                  </button>
                )}
                <button
                  onClick={handleBackToHome}
                  className="w-full bg-white text-[#2b354d] border border-[#2b354d] py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  返回首頁
                </button>
              </div>
            </>
          )}

          {/* ATM 轉帳等待確認 */}
          {status === 'pending' && (
            <>
              <div className="text-green-500 text-6xl mb-4">✅</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">ATM 取號成功！</h1>

              {/* 檢查是否為 ATM 轉帳並顯示轉帳資訊 */}
              {paymentData?.PaymentType === '2' && paymentData?.PayNo ? (
                <>
                  <p className="text-gray-600 mb-6">
                    <strong>虛擬帳號已建立完成</strong>
                    <br />
                    請使用以下資訊完成 ATM 轉帳
                  </p>

                  {/* 轉帳資訊卡片 */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6 text-left">
                    <h3 className="font-bold text-blue-900 mb-4 text-center">轉帳資訊</h3>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-blue-700 font-medium">轉入銀行：</span>
                        <span className="text-blue-900 font-bold">
                          {formatBankInfo(paymentData.BankType)}
                        </span>
                      </div>

                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-blue-700 font-medium">虛擬帳號：</span>
                        <span className="text-blue-900 font-bold text-lg font-mono">
                          {paymentData.PayNo}
                        </span>
                      </div>

                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-blue-700 font-medium">轉帳金額：</span>
                        <span className="text-blue-900 font-bold text-lg">
                          NT$ {paymentData.TradeAmt ? Number(paymentData.TradeAmt).toLocaleString() : '0'}
                        </span>
                      </div>

                      {paymentData.ExpireDate && (
                        <div className="flex justify-between items-center py-2">
                          <span className="text-blue-700 font-medium">繳費期限：</span>
                          <span className="text-red-600 font-bold">
                            {formatExpireDate(paymentData.ExpireDate)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {orderNo && (
                    <p className="text-sm text-gray-500 mb-6">
                      訂單號碼：{orderNo}
                    </p>
                  )}

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <p className="text-green-800 text-sm">
                      <strong>✅ 取號成功！轉帳注意事項：</strong>
                      <br />
                      • 請使用 ATM 或網路銀行轉帳至上述虛擬帳號
                      <br />
                      • 轉帳完成後約 10-15 分鐘系統將自動確認
                      <br />
                      • 逾期未繳費訂單將自動取消
                      <br />
                      • 轉帳資訊已同步更新至您的訂單記錄
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-gray-600 mb-6">
                    您選擇了 ATM 轉帳付款。
                    <br />
                    虛擬帳號資訊已發送至您的信箱，
                    <br />
                    請於期限內完成轉帳。
                  </p>
                  {orderNo && (
                    <p className="text-sm text-gray-500 mb-6">
                      訂單號碼：{orderNo}
                    </p>
                  )}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p className="text-yellow-800 text-sm">
                      <strong>注意事項：</strong>
                      <br />
                      • 請於 3 個工作天內完成轉帳
                      <br />
                      • 轉帳完成後系統將自動確認
                      <br />
                      • 確認後將發送通知郵件
                    </p>
                  </div>
                </>
              )}

              <div className="space-y-3">
                <button
                  onClick={handleCheckOrder}
                  className="w-full bg-[#2b354d] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#1e2a3a] transition-colors cursor-pointer"
                >
                  查看訂單狀態
                </button>
                <button
                  onClick={handleBackToHome}
                  className="w-full bg-white text-[#2b354d] border border-[#2b354d] py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  返回首頁
                </button>
              </div>
            </>
          )}

        </div>
      </div>
    </div>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2b354d] mx-auto mb-4"></div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">處理中...</h1>
          <p className="text-gray-600">正在處理付款結果，請稍候</p>
        </div>
      </div>
    </div>
  );
}

export default function PaymentResultPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <PaymentResultContent />
    </Suspense>
  );
}
