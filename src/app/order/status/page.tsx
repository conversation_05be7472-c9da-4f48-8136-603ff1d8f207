'use client';

import { useState } from 'react';
import OrderStatusResult from '@/components/OrderStatusResult';

interface OrderData {
  orderNo: string;
  name: string;
  email: string;
  eventName: string;
  eventPrice: number;
  sessionTimes: string[];
  participationType: string;
  paymentStatus: string;
  paymentMethod: string;
  paymentCompletedAt: string;
  submittedAt: string;
  // 新增 PayUni 查詢的額外資訊
  tradeStatus?: string;
  gateway?: string;
  dataSource?: string;
  payuniTradeNo?: string;
  payuniPayNo?: string;
}

export default function OrderStatusPage() {
  const [orderNo, setOrderNo] = useState('');
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!orderNo.trim()) {
      setError('請輸入訂單號碼');
      return;
    }

    setIsLoading(true);
    setError(null);
    setOrderData(null);

    try {
      const response = await fetch('/api/order-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderNo: orderNo.trim()
        }),
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('找不到此訂單，請確認訂單號碼是否正確');
        }
        throw new Error('查詢失敗，請稍後再試');
      }

      const data: OrderData = await response.json();
      setOrderData(data);

    } catch (error) {
      console.error('查詢訂單失敗:', error);
      setError(error instanceof Error ? error.message : '查詢失敗');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetryPayment = async () => {
    if (orderData) {
      try {
        // 使用 API 驗證訂單並取得付款資料
        const response = await fetch('/api/retry-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ orderNo: orderData.orderNo }),
        });

        const result = await response.json();

        if (response.ok && result.paymentData) {
          // 建立隱藏表單並自動提交到 PayUni
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = result.paymentData.action;
          form.style.display = 'none';

          // 添加必要的 PayUni 參數
          const fields = {
            MerID: result.paymentData.MerID,
            EncryptInfo: result.paymentData.EncryptInfo,
            HashInfo: result.paymentData.HashInfo,
            Version: result.paymentData.Version
          };

          Object.entries(fields).forEach(([name, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = name;
            input.value = value;
            form.appendChild(input);
          });

          document.body.appendChild(form);
          form.submit();
        } else {
          // 失敗，顯示錯誤訊息
          alert(result.error || '無法重新付款');
        }
      } catch (error) {
        console.error('重新付款失敗:', error);
        alert('處理失敗，請稍後再試');
      }
    }
  };



  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 頁面標題 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">訂單查詢</h1>
          <p className="text-gray-600">請輸入您的訂單號碼來查詢訂單狀態</p>
        </div>

        {/* 查詢表單 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div>
              <label htmlFor="orderNo" className="block text-sm font-medium text-gray-700 mb-2">
                訂單號碼 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="orderNo"
                name="orderNo"
                value={orderNo}
                onChange={(e) => setOrderNo(e.target.value)}
                placeholder="例如：pangea_1234567890"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>



            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4" data-testid="error-message">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                isLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'text-white shadow-lg hover:shadow-xl'
              }`}
              style={!isLoading ? { backgroundColor: '#2b354d' } : {}}
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  查詢中...
                </div>
              ) : (
                '查詢訂單'
              )}
            </button>
          </form>
        </div>

        {/* 訂單結果 */}
        {orderData && (
          <div data-testid="order-result">
            <OrderStatusResult orderData={orderData} />
          </div>
        )}

        {/* 重新付款和返回按鈕 - 只在有實際操作選項時顯示 */}
        {orderData && (
          (orderData.paymentStatus === '重新付款中') ||
          (orderData.paymentStatus === '待付款')
        ) && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">操作選項</h2>
            </div>

            {/* 重新付款中狀態的處理 */}
            {orderData.paymentStatus === '重新付款中' && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <div className="text-blue-500 text-2xl mr-3">🔄</div>
                    <h3 className="font-bold text-blue-900">重新付款進行中</h3>
                  </div>
                  <div className="text-blue-800 text-sm space-y-2">
                    <p><strong>您的重新付款請求正在處理中</strong></p>
                    <p>• 如果您已完成付款，請等待系統確認</p>
                    <p>• 如果您尚未付款，可以點擊下方按鈕繼續付款</p>
                    <p>• 如果遇到問題，可以重新發起付款請求</p>
                  </div>
                </div>
                <button
                  onClick={handleRetryPayment}
                  className="w-full bg-[#2b354d] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#1e2a3a] transition-colors cursor-pointer"
                >
                  重新發起付款
                </button>
              </div>
            )}

            {/* 待付款狀態的處理 */}
            {orderData.paymentStatus === '待付款' && (
              <div className="space-y-4">
                {/* 檢查是否為 ATM 轉帳 */}
                {orderData.paymentMethod === 'ATM轉帳' || orderData.paymentMethod === 'ATM 轉帳' ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <div className="text-blue-500 text-2xl mr-3">🏧</div>
                      <h3 className="font-bold text-blue-900">ATM 轉帳待付款</h3>
                    </div>
                    <div className="text-blue-800 text-sm space-y-2">
                      <p><strong>請完成 ATM 轉帳，系統將自動確認付款</strong></p>
                      <p>• 請使用 ATM 或網路銀行轉帳至虛擬帳號</p>
                      <p>• 轉帳完成後約 10-15 分鐘系統將自動確認</p>
                      <p>• 確認後狀態會更新為「已付款」並發送通知郵件</p>
                      <p>• 如超過繳費期限，狀態會更新為「已失效」</p>
                    </div>
                  </div>
                ) : orderData.paymentMethod === '' || !orderData.paymentMethod ? (
                  <div className="space-y-3">
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <div className="text-yellow-500 text-xl mr-2">⚠️</div>
                        <h3 className="font-medium text-yellow-900">尚未選擇付款方式</h3>
                      </div>
                      <p className="text-yellow-800 text-sm">
                        此訂單尚未完成付款流程，請點擊下方按鈕選擇付款方式。
                      </p>
                    </div>
                    <button
                      onClick={handleRetryPayment}
                      className="w-full bg-[#2b354d] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#1e2a3a] transition-colors cursor-pointer"
                    >
                      選擇付款方式
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={handleRetryPayment}
                    className="w-full bg-white text-[#2b354d] border border-[#2b354d] py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors cursor-pointer"
                  >
                    前往付款
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* 返回按鈕 */}
        <div className="text-center mt-8">
          <button
            onClick={() => window.location.href = '/movement-assembling-booking'}
            className="text-[#2b354d] hover:text-[#1a2332] font-medium cursor-pointer"
          >
            ← 返回報名頁面
          </button>
        </div>
      </div>
    </div>
  );
}
