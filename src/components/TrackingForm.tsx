'use client';

import { useState } from 'react';
import { trackClientEvent } from '@/lib/tracking-manager';

interface TrackingFormProps {
  formType: 'booking' | 'registration' | 'contact';
  eventType?: string;
  value?: number;
}

interface SubmitResult {
  success: boolean;
  message: string;
  trackingResult?: unknown;
}

export default function TrackingForm({ 
  formType, 
  eventType = 'general',
  value 
}: TrackingFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState<SubmitResult | null>(null);

  // 表單開始填寫時觸發 (可選)
  const handleFormStart = () => {
    // 發送瀏覽器端事件到 GTM
    trackClientEvent('form_start', {
      form_name: `${formType}_form`,
      event_type: eventType,
    });
  };

  // 表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 發送瀏覽器端事件到 GTM (表單提交開始)
      trackClientEvent('form_submit_start', {
        form_name: `${formType}_form`,
        event_type: eventType,
      });

      // 提交到後端 API (會觸發 CAPI 事件)
      const response = await fetch('/api/form-submit-example', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          formType,
          eventType,
          value,
        }),
      });

      const result = await response.json();
      setSubmitResult(result);

      if (result.success) {
        // 發送瀏覽器端成功事件到 GTM
        trackClientEvent('form_submit_success', {
          form_name: `${formType}_form`,
          event_type: eventType,
          tracking_result: result.trackingResult,
        });

        // 重置表單
        setFormData({ name: '', email: '', phone: '' });
      } else {
        // 發送瀏覽器端錯誤事件到 GTM
        trackClientEvent('form_submit_error', {
          form_name: `${formType}_form`,
          event_type: eventType,
          error: result.message,
        });
      }

    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitResult({
        success: false,
        message: '網路錯誤，請稍後再試',
      });

      // 發送瀏覽器端錯誤事件到 GTM
      trackClientEvent('form_submit_error', {
        form_name: `${formType}_form`,
        event_type: eventType,
        error: 'network_error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getFormTitle = () => {
    switch (formType) {
      case 'booking':
        return '預約表單';
      case 'registration':
        return '註冊表單';
      case 'contact':
        return '聯絡表單';
      default:
        return '表單';
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">{getFormTitle()}</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            姓名
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            onFocus={handleFormStart} // 第一次聚焦時觸發
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            電話
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? '提交中...' : '提交'}
        </button>
      </form>

      {/* 結果顯示 */}
      {submitResult && (
        <div className={`mt-4 p-3 rounded-md ${
          submitResult.success 
            ? 'bg-green-100 border border-green-300 text-green-800'
            : 'bg-red-100 border border-red-300 text-red-800'
        }`}>
          <p className="text-sm">{String(submitResult.message)}</p>
          
          {/* 開發模式下顯示追蹤結果 */}
          {process.env.NODE_ENV === 'development' && submitResult.trackingResult ? (
            <details className="mt-2">
              <summary className="cursor-pointer text-xs">追蹤結果 (開發模式)</summary>
              <pre className="text-xs mt-1 overflow-auto">
                {JSON.stringify(submitResult.trackingResult, null, 2)}
              </pre>
            </details>
          ) : null}
        </div>
      )}
    </div>
  );
}
