'use client';

import Script from 'next/script';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { GTM_CONFIG } from '@/config/environment-config';

// GTM 設定
const GTM_ID = GTM_CONFIG.getGTMId();
const GTM_PROXY_DOMAIN = GTM_CONFIG.getProxyDomain() || 'gtm.pangea.weaven.co';

// 開發環境下的 GTM 設定
const isDev = process.env.NODE_ENV === 'development';

const GTMContainer = () => {
  const pathname = usePathname();

  // 路由變更時觸發頁面瀏覽事件
  useEffect(() => {
    if (!GTM_ID) return;

    // 確保 dataLayer 存在
    if (typeof window !== 'undefined') {
      window.dataLayer = window.dataLayer || [];
      
      // 推送頁面瀏覽事件
      window.dataLayer.push({
        event: 'page_view',
        page_path: pathname,
        page_title: document.title,
      });
    }
  }, [pathname]);

  // 如果沒有設定 GTM ID，顯示警告並返回 null
  useEffect(() => {
    if (!GTM_ID) {
      console.warn('GTM ID is not set. Please set NEXT_PUBLIC_GTM_ID in your environment variables.');
    }
  }, []);

  if (!GTM_ID) {
    return null;
  }

  // 決定使用的 GTM 網域
  // 在開發環境下也使用 proxy 來測試功能
  const gtmDomain = isDev
    ? 'localhost:3000/api/gtm-proxy'  // 開發環境使用本地 proxy
    : GTM_PROXY_DOMAIN;               // 生產環境使用 Proxy 網域

  return (
    <>
      {/* GTM Script */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'http${isDev ? '' : 's'}://${gtmDomain}/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `,
        }}
      />

      {/* GTM NoScript Fallback */}
      <noscript>
        <iframe
          src={`http${isDev ? '' : 's'}://${gtmDomain}/ns.html?id=${GTM_ID}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  );
};

export default GTMContainer;
