import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react';
import AutoCarousel from '../AutoCarousel';

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, fill, priority, ...props }: {
    src: string;
    alt: string;
    fill?: boolean;
    priority?: boolean;
    [key: string]: unknown
  }) {
    // 過濾掉 Next.js 特有的屬性，避免 DOM 警告
    const { fill: _, priority: __, ...domProps } = props;
    return <img src={src} alt={alt} {...domProps} />;
  };
});

const mockSlides = [
  {
    id: 1,
    image: '/test-image-1.jpg',
    alt: 'Test Image 1',
    title: 'Test Title 1',
    description: 'Test Description 1'
  },
  {
    id: 2,
    image: '/test-image-2.jpg',
    alt: 'Test Image 2',
    title: 'Test Title 2',
    description: 'Test Description 2'
  },
  {
    id: 3,
    image: '/test-image-3.jpg',
    alt: 'Test Image 3',
    title: 'Test Title 3',
    description: 'Test Description 3'
  }
];

describe('AutoCarousel', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders carousel with slides', () => {
    render(<AutoCarousel slides={mockSlides} />);
    
    // Check if first slide is visible
    expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
    expect(screen.getByText('Test Title 1')).toBeInTheDocument();
    expect(screen.getByText('Test Description 1')).toBeInTheDocument();
  });

  it('renders slide indicators', () => {
    render(<AutoCarousel slides={mockSlides} />);
    
    // Should have 3 indicators for 3 slides
    const indicators = screen.getAllByRole('button');
    const slideIndicators = indicators.filter(button => 
      button.getAttribute('aria-label')?.includes('前往第')
    );
    expect(slideIndicators).toHaveLength(3);
  });

  it('auto-advances slides', async () => {
    render(<AutoCarousel slides={mockSlides} interval={1000} />);
    
    // Initially shows first slide
    expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
    
    // Advance time by 1 second
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    
    // Should now show second slide
    await waitFor(() => {
      const secondSlide = screen.getByAltText('Test Image 2');
      expect(secondSlide).toBeInTheDocument();
    });
  });

  it('navigates with indicator clicks', () => {
    render(<AutoCarousel slides={mockSlides} />);
    
    // Click on third indicator
    const indicators = screen.getAllByRole('button');
    const thirdIndicator = indicators.find(button => 
      button.getAttribute('aria-label') === '前往第 3 張投影片'
    );
    
    if (thirdIndicator) {
      fireEvent.click(thirdIndicator);
      expect(screen.getByAltText('Test Image 3')).toBeInTheDocument();
    }
  });

  it('navigates with arrow buttons', () => {
    render(<AutoCarousel slides={mockSlides} />);
    
    // Find navigation buttons
    const prevButton = screen.getByLabelText('上一張投影片');
    const nextButton = screen.getByLabelText('下一張投影片');
    
    // Click next button
    fireEvent.click(nextButton);
    expect(screen.getByAltText('Test Image 2')).toBeInTheDocument();
    
    // Click previous button
    fireEvent.click(prevButton);
    expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
  });

  it('handles keyboard navigation', () => {
    render(<AutoCarousel slides={mockSlides} />);

    const carousel = document.querySelector('[tabindex="0"]');

    if (carousel) {
      // Press right arrow
      fireEvent.keyDown(carousel, { key: 'ArrowRight' });
      expect(screen.getByAltText('Test Image 2')).toBeInTheDocument();

      // Press left arrow
      fireEvent.keyDown(carousel, { key: 'ArrowLeft' });
      expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
    }
  });

  it('pauses auto-play on mouse hover', () => {
    render(<AutoCarousel slides={mockSlides} interval={1000} />);
    
    const carousel = document.querySelector('[tabindex="0"]');
    
    if (carousel) {
      // Hover over carousel
      fireEvent.mouseEnter(carousel);
      
      // Advance time - should not change slide
      act(() => {
        jest.advanceTimersByTime(2000);
      });
      
      expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
      
      // Mouse leave should resume auto-play
      fireEvent.mouseLeave(carousel);
      
      act(() => {
        jest.advanceTimersByTime(1000);
      });
      
      expect(screen.getByAltText('Test Image 2')).toBeInTheDocument();
    }
  });

  it('handles touch swipe gestures', () => {
    render(<AutoCarousel slides={mockSlides} />);
    
    const carousel = document.querySelector('[tabindex="0"]');
    
    if (carousel) {
      // Simulate left swipe (should go to next slide)
      fireEvent.touchStart(carousel, {
        targetTouches: [{ clientX: 100 }]
      });
      fireEvent.touchMove(carousel, {
        targetTouches: [{ clientX: 40 }]
      });
      fireEvent.touchEnd(carousel);
      
      expect(screen.getByAltText('Test Image 2')).toBeInTheDocument();
      
      // Simulate right swipe (should go to previous slide)
      fireEvent.touchStart(carousel, {
        targetTouches: [{ clientX: 40 }]
      });
      fireEvent.touchMove(carousel, {
        targetTouches: [{ clientX: 100 }]
      });
      fireEvent.touchEnd(carousel);
      
      expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
    }
  });

  it('renders title and subtitle when provided', () => {
    render(
      <AutoCarousel 
        slides={mockSlides} 
        title="Test Carousel Title"
        subtitle="Test Carousel Subtitle"
      />
    );
    
    expect(screen.getByText('Test Carousel Title')).toBeInTheDocument();
    expect(screen.getByText('Test Carousel Subtitle')).toBeInTheDocument();
  });

  it('returns null when no slides provided', () => {
    const { container } = render(<AutoCarousel slides={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('does not auto-advance when only one slide', () => {
    const singleSlide = [mockSlides[0]];
    render(<AutoCarousel slides={singleSlide} interval={1000} />);
    
    expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
    
    // Advance time
    act(() => {
      jest.advanceTimersByTime(2000);
    });
    
    // Should still show the same slide
    expect(screen.getByAltText('Test Image 1')).toBeInTheDocument();
  });
});
