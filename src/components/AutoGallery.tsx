"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface GalleryImage {
  id: number;
  image: string;
  alt: string;
  title?: string;
}

interface AutoGalleryProps {
  images: GalleryImage[];
  autoScroll?: boolean; // enable auto scroll, default true
  speed?: number; // pixels per second, default 50
  title?: string;
  subtitle?: string;
}

const AutoGallery = ({
  images,
  autoScroll = true,
  speed = 50,
  title,
  subtitle
}: AutoGalleryProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // 檢查滾動狀態
  const checkScrollButtons = () => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    setCanScrollLeft(scrollContainer.scrollLeft > 0);
    setCanScrollRight(
      scrollContainer.scrollLeft < scrollContainer.scrollWidth - scrollContainer.clientWidth
    );
  };

  // 滾動功能
  const scrollLeft = () => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    scrollContainer.scrollBy({
      left: -400,
      behavior: 'smooth'
    });
  };

  const scrollRight = () => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    scrollContainer.scrollBy({
      left: 400,
      behavior: 'smooth'
    });
  };

  // 自動滾動效果
  useEffect(() => {
    if (!autoScroll || isHovered) return;

    const scrollContainer = scrollRef.current;
    if (!scrollContainer || images.length === 0) return;

    let animationId: number;
    let scrollPosition = 0;

    const animate = () => {
      scrollPosition += speed / 60; // 60fps

      // Reset position when we've scrolled past half the content
      // (since we duplicate the images, we reset at halfway point)
      const maxScroll = scrollContainer.scrollWidth / 2;
      if (scrollPosition >= maxScroll) {
        scrollPosition = 0;
      }

      scrollContainer.scrollLeft = scrollPosition;
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [speed, images.length, autoScroll, isHovered]);

  // 監聽滾動事件
  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    // 初始檢查
    const timer = setTimeout(() => {
      checkScrollButtons();
    }, 100);

    checkScrollButtons();
    scrollContainer.addEventListener('scroll', checkScrollButtons);

    return () => {
      clearTimeout(timer);
      scrollContainer.removeEventListener('scroll', checkScrollButtons);
    };
  }, [images, autoScroll]);

  if (images.length === 0) return null;

  // Duplicate images for seamless loop (only if auto scroll is enabled)
  const displayImages = autoScroll ? [...images, ...images] : images;

  return (
    <section className="py-24 bg-gradient-to-br from-muted/60 via-background to-muted/40 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.05),transparent_50%)]" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        {(title || subtitle) && (
          <div className="text-center mb-20">
            {title && (
              <h2 className="text-2xl md:text-4xl font-bold text-foreground mb-6 bg-gradient-to-r from-foreground via-accent to-foreground bg-clip-text text-transparent">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-base md:text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Scrolling Gallery */}
      <div className="relative">
        {/* 左側滾動按鈕 */}
        {!autoScroll && canScrollLeft && (
          <button
            onClick={scrollLeft}
            className={cn(
              "absolute left-4 top-1/2 -translate-y-1/2 z-10",
              "w-12 h-12 rounded-full bg-primary/90 text-primary-foreground",
              "flex items-center justify-center shadow-lg",
              "hover:bg-primary hover:scale-110 transition-all duration-200",
              "backdrop-blur-sm border border-primary-foreground/20"
            )}
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
        )}

        {/* 右側滾動按鈕 */}
        {!autoScroll && canScrollRight && (
          <button
            onClick={scrollRight}
            className={cn(
              "absolute right-4 top-1/2 -translate-y-1/2 z-10",
              "w-12 h-12 rounded-full bg-primary/90 text-primary-foreground",
              "flex items-center justify-center shadow-lg",
              "hover:bg-primary hover:scale-110 transition-all duration-200",
              "backdrop-blur-sm border border-primary-foreground/20"
            )}
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        )}

        <div
          className="overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div
            ref={scrollRef}
            className={cn(
              "flex space-x-6",
              autoScroll ? "overflow-x-hidden" : "overflow-x-auto scrollbar-hide"
            )}
            style={{ width: "fit-content" }}
          >
            {displayImages.map((image, index) => (
            <div
              key={`${image.id}-${index}`}
              className="flex-shrink-0 group"
            >
              <div className={cn(
                "relative w-72 sm:w-80 lg:w-96 h-48 sm:h-60 lg:h-72 rounded-xl overflow-hidden",
                "border border-border/50 shadow-lg hover:shadow-2xl",
                "hover:shadow-accent/20 transition-all duration-300",
                "hover:border-accent/50"
              )}>
                <Image
                  src={image.image}
                  alt={image.alt}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />

                {/* Image Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Image Title */}
                {image.title && (
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className={cn(
                      "text-primary-foreground font-semibold text-base",
                      "opacity-0 group-hover:opacity-100 transition-all duration-300",
                      "transform translate-y-2 group-hover:translate-y-0"
                    )}>
                      {image.title}
                    </h3>
                  </div>
                )}
              </div>
            </div>
          ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AutoGallery;
