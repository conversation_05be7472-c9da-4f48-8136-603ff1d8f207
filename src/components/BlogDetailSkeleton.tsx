export default function BlogDetailSkeleton() {
  return (
    <div className="min-h-screen bg-white">
      {/* 返回按鈕骨架 */}
      <div className="border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-200 rounded mr-2 animate-pulse"></div>
            <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* 主要內容骨架 */}
      <article className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 文章標題骨架 */}
          <header className="mb-8">
            <div className="space-y-4 mb-6">
              <div className="h-12 bg-gray-200 rounded w-full animate-pulse"></div>
              <div className="h-12 bg-gray-200 rounded w-3/4 animate-pulse"></div>
            </div>

            {/* 文章資訊骨架 */}
            <div className="flex items-center mb-8">
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </header>

          {/* Hero 圖片骨架 */}
          <div className="mb-12">
            <div className="relative w-full h-96 bg-gray-200 rounded-xl animate-pulse">
              <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer rounded-xl"></div>
            </div>
          </div>

          {/* 文章內容骨架 */}
          <div className="mb-12 space-y-4">
            {/* 段落骨架 */}
            {[...Array(8)].map((_, index) => (
              <div key={index} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                {index % 3 === 0 && (
                  <div className="my-6">
                    <div className="h-8 bg-gray-200 rounded w-1/2 animate-pulse mb-4"></div>
                  </div>
                )}
              </div>
            ))}

            {/* 圖片骨架 */}
            <div className="my-8">
              <div className="relative w-full h-64 bg-gray-200 rounded-lg animate-pulse">
                <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer rounded-lg"></div>
              </div>
            </div>

            {/* 更多段落骨架 */}
            {[...Array(6)].map((_, index) => (
              <div key={`more-${index}`} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-4/5 animate-pulse"></div>
              </div>
            ))}

            {/* 表格骨架 */}
            <div className="my-8">
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                {[...Array(4)].map((_, rowIndex) => (
                  <div key={rowIndex} className="flex border-b border-gray-200 last:border-b-0">
                    <div className="flex-1 p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                    </div>
                    <div className="flex-1 p-4">
                      <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA 按鈕骨架 */}
            <div className="text-center my-8">
              <div className="inline-block w-48 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
          </div>

          {/* 相關文章骨架 */}
          <div className="border-t border-slate-200 pt-8">
            <div className="w-32 h-6 bg-gray-200 rounded mb-6 animate-pulse"></div>
            <div className="grid gap-6 md:grid-cols-2">
              {[...Array(2)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg border border-slate-200 overflow-hidden">
                  <div className="relative w-full h-48 bg-gray-200 animate-pulse">
                    <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer"></div>
                  </div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"></div>
                    <div className="space-y-2 mb-3">
                      <div className="h-5 bg-gray-200 rounded w-full animate-pulse"></div>
                      <div className="h-5 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}
