const VideoBackground = ({ src }: { src: string }) => {
  return (
    <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
      <video
        autoPlay
        loop
        muted
        playsInline
        className="w-full h-full object-cover"
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      <div className="absolute top-0 left-0 w-full h-full bg-black/50" />
    </div>
  );
};

export default VideoBackground;
