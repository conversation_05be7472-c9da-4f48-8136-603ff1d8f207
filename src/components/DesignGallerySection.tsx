"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { X, ChevronLeft, ChevronRight } from "lucide-react";

interface GalleryImage {
  id: number;
  image: string;
  alt: string;
}

interface DesignGallerySectionProps {
  title: string;
  textContent: string[];
  images: GalleryImage[];
}

const DesignGallerySection = ({
  title,
  textContent,
  images
}: DesignGallerySectionProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  // 鍵盤事件監聽
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') closeLightbox();
      if (e.key === 'ArrowRight') nextImage();
      if (e.key === 'ArrowLeft') prevImage();
    };

    if (lightboxOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [lightboxOpen, images.length, nextImage, prevImage]);

  return (
    <>
      <section className="py-12 md:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* 左側：文字內容 */}
            <div className="space-y-6 text-center lg:text-left">
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-8">
                {title}
              </h2>

              <div className="space-y-4">
                {textContent.map((text, index) => (
                  <p
                    key={index}
                    className="text-base md:text-lg leading-relaxed"
                    style={{ color: '#2b354d' }}
                  >
                    {text}
                  </p>
                ))}
              </div>
            </div>

            {/* 右側：圖片 Gallery */}
            <div className="flex gap-4">
              {/* 左側大圖 */}
              <div
                className="relative flex-1 h-64 md:h-80 overflow-hidden rounded-xl cursor-pointer group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => openLightbox(0)}
              >
                <Image
                  src={images[0].image}
                  alt={images[0].alt}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
              </div>

              {/* 右側三張小圖垂直排列 */}
              <div className="flex flex-col gap-2 w-24 md:w-32">
                {images.slice(1).map((image, index) => (
                  <div
                    key={image.id}
                    className="relative h-20 md:h-24 overflow-hidden rounded-lg cursor-pointer group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                    onClick={() => openLightbox(index + 1)}
                  >
                    <Image
                      src={image.image}
                      alt={image.alt}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Lightbox */}
      {lightboxOpen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center">
          {/* 關閉按鈕 */}
          <button
            onClick={closeLightbox}
            className="absolute top-4 right-4 z-60 p-2 transition-colors"
            style={{ color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#f1f5f9'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#ffffff'}
          >
            <X size={32} />
          </button>

          {/* 上一張按鈕 */}
          <button
            onClick={prevImage}
            className="absolute left-4 z-60 p-2 transition-colors"
            style={{ color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#f1f5f9'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#ffffff'}
          >
            <ChevronLeft size={32} />
          </button>

          {/* 下一張按鈕 */}
          <button
            onClick={nextImage}
            className="absolute right-4 z-60 p-2 transition-colors"
            style={{ color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#f1f5f9'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#ffffff'}
          >
            <ChevronRight size={32} />
          </button>

          {/* 主圖片 */}
          <div className="relative w-full h-full max-w-4xl max-h-[80vh] mx-4">
            <Image
              src={images[currentImageIndex].image}
              alt={images[currentImageIndex].alt}
              fill
              className="object-contain"
              priority
            />
          </div>

          {/* 圖片指示器 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                )}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default DesignGallerySection;
