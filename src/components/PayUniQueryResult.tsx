import React from 'react';

interface PayUniResultData {
  Status?: string;
  Message?: string;
  [key: string]: unknown; // 允許動態的 Result[0][...] 欄位
}

interface PayUniQueryResultProps {
  data: PayUniResultData;
}

// 解析 PayUni API 回應中的 Result[0][...] 欄位
function parsePayUniResult(data: PayUniResultData) {
  const result: Record<string, unknown> = {};
  
  // 提取所有 Result[0][...] 欄位
  Object.keys(data).forEach(key => {
    const match = key.match(/^Result\[0\]\[(.+)\]$/);
    if (match) {
      result[match[1]] = data[key];
    }
  });
  
  return result;
}

// 格式化顯示值
function formatValue(key: string, value: unknown): string {
  if (value === null || value === undefined || value === '') {
    return '無資料';
  }

  switch (key) {
    case 'TradeAmt':
    case 'TradeFee':
    case 'CloseAmt':
    case 'RemainAmt':
    case 'RefundAmt':
      return `NT$ ${parseInt(String(value || '0')).toLocaleString()}`;

    case 'TradeStatus':
      switch (value) {
        case '0': return '取號成功';
        case '1': return '已付款';
        case '2': return '付款失敗';
        case '3': return '付款取消';
        case '4': return '交易逾期';
        case '8': return '訂單待確認';
        case '9': return '待付款';
        default: return '未知狀態';
      }

    case 'PaymentType':
      switch (value) {
        case '1': return '信用卡';
        case '2': return 'ATM 轉帳';
        case '3': return '超商代碼';
        case '4': return '超商條碼';
        default: return `付款方式 ${value}`;
      }

    case 'Gateway':
      return value === '2' ? '信用卡閘道' : `閘道 ${value}`;

    case 'AuthType':
      return value === '1' ? '3D 驗證' : `驗證類型 ${value}`;

    case 'CloseStatus':
      return value === '2' ? '已請款' : value === '1' ? '未請款' : `請款狀態 ${value}`;

    case 'RefundStatus':
      return value === '2' ? '已退款' : value === '1' ? '未退款' : `退款狀態 ${value}`;

    case 'RefundType':
      return value === '2' ? '部分退款' : value === '1' ? '全額退款' : `退款類型 ${value}`;

    case 'Card6No':
    case 'Card4No':
      return `****${value}`;

    default:
      return String(value);
  }
}

// 欄位顯示名稱對應
const fieldLabels: Record<string, string> = {
  'MerTradeNo': '商店訂單號碼',
  'TradeNo': 'PayUni 交易號碼',
  'TradeAmt': '交易金額',
  'TradeFee': '手續費',
  'TradeStatus': '交易狀態',
  'PaymentType': '付款方式',
  'PaymentDay': '付款時間',
  'CreateDay': '建立時間',
  'Gateway': '付款閘道',
  'DataSource': '資料來源',
  'Card6No': '卡號前六碼',
  'Card4No': '卡號後四碼',
  'CardExp': '卡片到期',
  'CardInst': '分期期數',
  'AuthCode': '授權碼',
  'AuthType': '驗證類型',
  'CardBank': '發卡銀行代碼',
  'CloseStatus': '請款狀態',
  'CloseAmt': '請款金額',
  'RemainAmt': '剩餘金額',
  'RefundType': '退款類型',
  'RefundStatus': '退款狀態',
  'RefundAmt': '退款金額',
  'RefundDay': '退款時間',
  'OffChannel': '取號通路',
  'OffPayChannel': '繳費通路',
  'OffPayNo': '繳費代碼',
  'OffExpireTime': '繳費期限'
};

// 重要欄位順序
const importantFields = [
  'MerTradeNo',
  'TradeNo', 
  'TradeStatus',
  'PaymentType',
  'TradeAmt',
  'PaymentDay',
  'TradeFee'
];

export default function PayUniQueryResult({ data }: PayUniQueryResultProps) {
  const result = parsePayUniResult(data);
  const hasResult = Object.keys(result).length > 0;

  if (!hasResult) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-medium text-yellow-900 mb-2">查詢結果格式異常</h3>
        <p className="text-yellow-700 text-sm">無法解析 PayUni API 回應格式</p>
        <details className="mt-3">
          <summary className="cursor-pointer text-yellow-800 text-sm font-medium">
            原始回應資料
          </summary>
          <pre className="mt-2 p-3 bg-yellow-100 rounded text-xs overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 查詢狀態 */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="font-medium text-green-900 mb-2">
          {data.Status === 'SUCCESS' ? '✅ 查詢成功' : '❌ 查詢失敗'}
        </h3>
        <p className="text-green-700 text-sm">{data.Message || '已成功從 PayUni API 獲取訂單資訊'}</p>
      </div>

      {/* 重要資訊 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-3">📋 重要資訊</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          {importantFields.map(field => {
            if (result[field] !== undefined) {
              return (
                <div key={field}>
                  <span className="text-blue-600 font-medium">{fieldLabels[field] || field}:</span>
                  <p className="font-medium text-blue-900">{formatValue(field, result[field])}</p>
                </div>
              );
            }
            return null;
          })}
        </div>
      </div>

      {/* 付款詳情 */}
      {(result.Card6No || result.Card4No || result.AuthCode) ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">💳 付款詳情</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {['Card6No', 'Card4No', 'CardExp', 'CardInst', 'AuthCode', 'AuthType', 'CardBank'].map(field => {
              if (result[field] !== undefined && result[field] !== null && result[field] !== '') {
                return (
                  <div key={field}>
                    <span className="text-gray-600">{fieldLabels[field] || field}:</span>
                    <p className="font-medium">{formatValue(field, result[field])}</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      ) : null}

      {/* 請款與退款資訊 */}
      {(result.CloseStatus || result.RefundStatus) ? (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="font-medium text-purple-900 mb-3">💰 請款與退款</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {['CloseStatus', 'CloseAmt', 'RemainAmt', 'RefundType', 'RefundStatus', 'RefundAmt', 'RefundDay'].map(field => {
              if (result[field] !== undefined && result[field] !== null && result[field] !== '') {
                return (
                  <div key={field}>
                    <span className="text-purple-600">{fieldLabels[field] || field}:</span>
                    <p className="font-medium text-purple-900">{formatValue(field, result[field])}</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      ) : null}

      {/* ATM 轉帳資訊 */}
      {(result.PaymentType === '2' && (result.OffChannel || result.OffPayNo || result.OffPayChannel || result.OffExpireTime)) ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-900 mb-3">🏧 ATM 轉帳資訊</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {['OffChannel', 'OffPayChannel', 'OffPayNo', 'OffExpireTime'].map(field => {
              if (result[field] !== undefined && result[field] !== null && result[field] !== '') {
                return (
                  <div key={field}>
                    <span className="text-green-600">{fieldLabels[field] || field}:</span>
                    <p className="font-medium text-green-900">{String(result[field])}</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      ) : null}

      {/* 完整資料 */}
      <details className="bg-gray-50 rounded-lg p-4">
        <summary className="cursor-pointer font-medium text-gray-900 mb-2">
          🔍 完整 PayUni API 回應
        </summary>
        <div className="mt-3 space-y-3">
          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium text-gray-800 mb-2">解析後的訂單資料</h4>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
          <div className="bg-white p-3 rounded border">
            <h4 className="font-medium text-gray-800 mb-2">原始 API 回應</h4>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        </div>
      </details>
    </div>
  );
}
