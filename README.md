# Pangea Website

一個專業的錶盒相關網站，提供錶款展示、活動報名、部落格文章和客戶服務等功能。

## 📋 專案描述

Pangea Website 是一個現代化的錶盒展示與服務網站，整合了多項核心功能：

- **錶款展示**：精選錶款的詳細介紹與展示
- **活動報名**：錶匠體驗活動的線上報名系統
- **部落格系統**：錶款相關的精選文章與資訊
- **客戶服務**：常見問題解答與客戶支援
- **錶盒預約**：專業的錶盒預約服務
- **金流整合**：支援信用卡與 ATM 轉帳付款

## 🛠 技術架構

### 核心技術棧

- **框架**：[Next.js 15](https://nextjs.org/) (App Router)
- **語言**：TypeScript
- **樣式**：[Tailwind CSS 4](https://tailwindcss.com/)
- **UI 組件**：[shadcn/ui](https://ui.shadcn.com/) + [Radix UI](https://www.radix-ui.com/)
- **圖標**：[Lucide React](https://lucide.dev/)

### 整合服務

- **金流**：PayUni (信用卡、ATM 轉帳)
- **資料管理**：Google Sheets API
- **分析追蹤**：Google Tag Manager (GTM) + Meta Pixel
- **部署**：Vercel

### 開發工具

- **測試**：Jest + Testing Library + MSW + Playwright
- **程式碼品質**：ESLint + TypeScript
- **包管理**：npm

## 🚀 環境設定

### 系統需求

- Node.js 18.0 或更高版本
- npm 9.0 或更高版本

### 本地開發環境設置

1. **克隆專案**
   ```bash
   git clone <repository-url>
   cd pangea-website
   ```

2. **安裝依賴**
   ```bash
   npm install
   ```

3. **環境變數配置**

   複製環境變數範本：
   ```bash
   cp .env.example .env.local
   ```

4. **啟動開發伺服器**
   ```bash
   npm run dev
   ```

   開啟瀏覽器訪問 [http://localhost:3000](http://localhost:3000)

### 環境變數配置

#### 必要配置

```bash
# PayUni 金流設定
APP_ENVIRONMENT=sandbox  # 或 production
PAYUNI_SANDBOX_MER_ID=your_sandbox_merchant_id
PAYUNI_SANDBOX_HASH_KEY=your_sandbox_hash_key
PAYUNI_SANDBOX_HASH_IV=your_sandbox_hash_iv
PAYUNI_PRODUCTION_MER_ID=your_production_merchant_id
PAYUNI_PRODUCTION_HASH_KEY=your_production_hash_key
PAYUNI_PRODUCTION_HASH_IV=your_production_hash_iv

# Google Sheets 整合
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
GOOGLE_SHEET_ID=your_main_sheet_id
GOOGLE_WATCH_SHEET_ID=your_watch_sheet_id
GOOGLE_BLOG_SHEET_ID=your_blog_sheet_id

# 分析追蹤
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
META_PIXEL_ID=your_pixel_id
META_ACCESS_TOKEN=your_access_token
META_TEST_EVENT_CODE=your_test_event_code
```

#### 環境切換

專案使用 `APP_ENVIRONMENT` 變數統一控制所有服務的環境：
- `sandbox`：測試環境（PayUni 沙盒、測試 GTM、測試 Meta Pixel）
- `production`：正式環境（PayUni 正式、正式 GTM、正式 Meta Pixel）

## ✨ 功能特色

### 金流整合
- **PayUni 金流**：支援信用卡一次性付款和 ATM 轉帳
- **訂單管理**：完整的訂單狀態追蹤與查詢
- **付款重試**：支援付款失敗後的重新付款
- **自動對帳**：PayUni 回調自動更新訂單狀態

### 資料管理
- **Google Sheets 整合**：報名資料、錶款資訊、部落格文章
- **即時同步**：表單提交即時寫入 Google Sheets
- **效能優化**：選擇性欄位讀取，減少 API 呼叫
- **快取策略**：優化資料載入速度

### 分析追蹤
- **GTM 整合**：透過代理伺服器避免廣告攔截
- **Meta CAPI**：伺服器端事件追蹤
- **UTM 參數**：Facebook 廣告歸因追蹤
- **事件對應**：不同表單對應不同追蹤事件

### 使用者體驗
- **響應式設計**：支援桌面、平板、手機
- **無障礙設計**：符合 WCAG 標準
- **效能優化**：圖片懶載入、虛擬滾動
- **SEO 優化**：結構化資料、sitemap、robots.txt

## 🧪 測試

### 測試架構

專案採用多層次測試策略：

1. **單元測試** - Jest + Testing Library
   - 測試個別函數和組件
   - 快速執行，高覆蓋率

2. **整合測試** - Jest + MSW
   - 測試 API 端點和服務整合
   - 模擬外部 API 調用

3. **E2E 測試** - Playwright
   - 測試完整用戶流程
   - 真實瀏覽器環境

### 測試指令

```bash
# 執行所有測試
npm test

# 監視模式
npm run test:watch

# 測試覆蓋率
npm run test:coverage

# 單元測試
npm run test:unit

# 整合測試
npm run test:integration

# API 測試套件
npm run test:api

# 特定 API 測試
npm run test:api:forms      # 表單提交測試
npm run test:api:payuni     # PayUni 整合測試
npm run test:api:sheets     # Google Sheets 測試
```

### 測試覆蓋範圍

- **表單提交流程**：註冊、聯絡、預約表單
- **PayUni 金流**：付款建立、回調處理、訂單查詢
- **Google Sheets**：資料讀寫、錯誤處理
- **頁面渲染**：所有主要頁面組件
- **響應式設計**：不同螢幕尺寸適配

詳細測試指南請參考：[docs/testing-guide.md](docs/testing-guide.md)

## 🚀 部署

### Vercel 部署

專案設計為在 Vercel 平台上部署：

1. **連接 GitHub**：將專案連接到 Vercel
2. **環境變數**：在 Vercel 控制台設定環境變數
3. **自動部署**：推送到 main 分支自動觸發部署

### 環境切換

使用 `APP_ENVIRONMENT` 環境變數控制：
- **測試環境**：設定為 `sandbox`
- **正式環境**：設定為 `production`



## 📁 專案結構

```
pangea-website/
├── src/
│   ├── app/                    # Next.js App Router 頁面
│   │   ├── api/               # API 路由
│   │   ├── blog/              # 部落格頁面
│   │   ├── pre-owned-watches/ # 錶款展示
│   │   ├── movement-assembling-booking/ # 錶匠體驗預約
│   │   ├── pangea-booking/    # 錶盒預約
│   │   └── support/           # 客戶支援
│   ├── components/            # React 組件
│   │   ├── ui/               # shadcn/ui 基礎組件
│   │   └── ...               # 業務組件
│   ├── lib/                   # 工具函數
│   ├── hooks/                 # React Hooks
│   ├── types/                 # TypeScript 類型定義
│   ├── utils/                 # 通用工具
│   ├── config/                # 配置檔案
│   └── __tests__/             # 測試檔案
├── public/                    # 靜態資源
├── docs/                      # 專案文件
├── coverage/                  # 測試覆蓋率報告
└── scripts/                   # 建置腳本
```

### 主要目錄說明

- **`src/app/api/`**：後端 API 路由，處理表單提交、付款、資料查詢
- **`src/components/`**：可重用的 React 組件
- **`src/lib/`**：核心業務邏輯，如 PayUni 整合、Google Sheets 操作
- **`src/hooks/`**：自定義 React Hooks

## 🔧 開發指令

```bash
# 開發
npm run dev              # 啟動開發伺服器 (port 3000)

# 建置
npm run build            # 建置正式版本
npm run start            # 啟動正式版本

# 程式碼品質
npm run lint             # ESLint 檢查

# 測試
npm test                 # 執行測試
npm run test:coverage    # 測試覆蓋率
npm run test:watch       # 監視模式測試
```

## 📚 相關文件

- [環境變數配置指南](docs/environment-variables-guide.md)
- [測試指南](docs/testing-guide.md)
- [API 測試報告](API_TESTING_REPORT.md)
- [CI/CD 指南](CI_CD_GUIDE.md)

## 🤝 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 授權

本專案為私有專案，版權所有。

## 📞 聯絡資訊

如有問題或建議，請聯絡開發團隊。
