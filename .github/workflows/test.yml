name: 自動化測試

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    name: 單元測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 執行 ESLint
      run: npm run lint
      
    - name: 執行單元測試
      run: npm run test:unit
      env:
        # 環境控制變數
        APP_ENVIRONMENT: sandbox
        PAYUNI_ENVIRONMENT: sandbox
        # PayUni 測試環境變數（單元測試最小集合）
        PAYUNI_SANDBOX_MER_ID: ${{ secrets.PAYUNI_SANDBOX_MER_ID }}
        PAYUNI_SANDBOX_HASH_KEY: ${{ secrets.PAYUNI_SANDBOX_HASH_KEY }}
        PAYUNI_SANDBOX_HASH_IV: ${{ secrets.PAYUNI_SANDBOX_HASH_IV }}
        # 基本 Google Sheets 變數（單元測試可能需要）
        GOOGLE_SERVICE_ACCOUNT_EMAIL: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_EMAIL }}
        GOOGLE_PRIVATE_KEY: ${{ secrets.GOOGLE_PRIVATE_KEY }}
        GOOGLE_SANDBOX_SHEET_ID: ${{ secrets.GOOGLE_SANDBOX_SHEET_ID }}
        
    - name: 上傳測試覆蓋率報告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        fail_ci_if_error: false

  integration-tests:
    name: 整合測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 執行整合測試
      run: npm run test:integration
      env:
        # 環境控制變數
        APP_ENVIRONMENT: sandbox
        PAYUNI_ENVIRONMENT: sandbox
        # PayUni 測試環境變數
        PAYUNI_SANDBOX_MER_ID: ${{ secrets.PAYUNI_SANDBOX_MER_ID }}
        PAYUNI_SANDBOX_HASH_KEY: ${{ secrets.PAYUNI_SANDBOX_HASH_KEY }}
        PAYUNI_SANDBOX_HASH_IV: ${{ secrets.PAYUNI_SANDBOX_HASH_IV }}
        # Google Sheets 測試環境變數
        GOOGLE_SERVICE_ACCOUNT_EMAIL: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_EMAIL }}
        GOOGLE_PRIVATE_KEY: ${{ secrets.GOOGLE_PRIVATE_KEY }}
        GOOGLE_SANDBOX_SHEET_ID: ${{ secrets.GOOGLE_SANDBOX_SHEET_ID }}
        GOOGLE_SANDBOX_WATCH_SHEET_ID: ${{ secrets.GOOGLE_SANDBOX_WATCH_SHEET_ID }}
        GOOGLE_SANDBOX_BLOG_SHEET_ID: ${{ secrets.GOOGLE_SANDBOX_BLOG_SHEET_ID }}
        # Meta Pixel 測試環境變數
        META_SANDBOX_PIXEL_ID: ${{ secrets.META_SANDBOX_PIXEL_ID }}
        META_SANDBOX_ACCESS_TOKEN: ${{ secrets.META_SANDBOX_ACCESS_TOKEN }}
        META_SANDBOX_TEST_EVENT_CODE: ${{ secrets.META_SANDBOX_TEST_EVENT_CODE }}
        # 其他必要變數
        NEXT_PUBLIC_BASE_URL: https://test.example.com



  build-test:
    name: 建置測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 測試建置
      run: npm run build
      env:
        # 環境控制變數
        APP_ENVIRONMENT: sandbox
        PAYUNI_ENVIRONMENT: sandbox
        # PayUni 測試用假值（建置測試用）
        PAYUNI_SANDBOX_MER_ID: TEST_MERCHANT_ID
        PAYUNI_SANDBOX_HASH_KEY: TEST_HASH_KEY_32_CHARACTERS_LONG
        PAYUNI_SANDBOX_HASH_IV: TEST_HASH_IV_16_CH
        # Google Sheets 測試用假值
        GOOGLE_SERVICE_ACCOUNT_EMAIL: <EMAIL>
        GOOGLE_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY_CONTENT\n-----END PRIVATE KEY-----\n"
        GOOGLE_SANDBOX_SHEET_ID: TEST_SHEET_ID
        GOOGLE_SANDBOX_WATCH_SHEET_ID: TEST_WATCH_SHEET_ID
        GOOGLE_SANDBOX_BLOG_SHEET_ID: TEST_BLOG_SHEET_ID
        # Meta Pixel 測試用假值
        META_SANDBOX_PIXEL_ID: "****************"
        META_SANDBOX_ACCESS_TOKEN: TEST_ACCESS_TOKEN
        META_SANDBOX_TEST_EVENT_CODE: TEST_EVENT_CODE
        # 其他建置必要變數
        NEXT_PUBLIC_BASE_URL: https://test.example.com
        NEXT_PUBLIC_GTM_SANDBOX_ID: GTM-TEST123
        NEXT_PUBLIC_GTM_SANDBOX_PROXY_DOMAIN: gtm.test.example.com
        NEXT_PUBLIC_CAPI_ENABLED: "false"
