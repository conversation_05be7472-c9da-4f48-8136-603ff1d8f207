require('@testing-library/jest-dom')

// Polyfill for TextEncoder/TextDecoder (needed for MSW)
const { TextEncoder, TextDecoder } = require('util')
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Polyfill for Web APIs needed by Next.js API routes
if (!global.Request) {
  // Simple polyfills for testing
  global.Request = class Request {
    constructor(url, options = {}) {
      // 使用 Object.defineProperty 來設定 url 屬性
      Object.defineProperty(this, 'url', {
        value: url,
        writable: false,
        enumerable: true,
        configurable: false
      })
      this.method = options.method || 'GET'
      this.headers = new Map(Object.entries(options.headers || {}))
      this.body = options.body
    }

    async json() {
      return JSON.parse(this.body)
    }

    async formData() {
      return this.body
    }
  }

  global.Response = class Response {
    constructor(body, options = {}) {
      this.body = body
      this.status = options.status || 200
      this.headers = new Map(Object.entries(options.headers || {}))
    }

    async json() {
      return JSON.parse(this.body)
    }

    static json(data, options = {}) {
      return new Response(JSON.stringify(data), {
        status: options.status || 200,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })
    }
  }

  global.Headers = Map

  // URL polyfill
  if (!global.URL) {
    global.URL = class URL {
      constructor(url, base) {
        if (base) {
          this.href = new URL(url, base).href
        } else {
          this.href = url
        }

        // 簡單的 URL 解析
        const match = this.href.match(/^(https?:)\/\/([^\/]+)(\/.*)?(\?.*)?$/)
        if (match) {
          this.protocol = match[1]
          this.host = match[2]
          this.pathname = match[3] || '/'
          this.search = match[4] || ''
        }
      }

      toString() {
        return this.href
      }
    }
  }

  global.FormData = class FormData {
    constructor() {
      this.data = new Map()
    }

    append(key, value) {
      this.data.set(key, value)
    }

    get(key) {
      return this.data.get(key)
    }

    entries() {
      return this.data.entries()
    }
  }
}

// 暫時禁用 MSW，專注於基本測試功能
// import { server } from './src/__tests__/mocks/server'

// 在所有測試開始前啟動 MSW 服務器
// beforeAll(() => {
//   server.listen({
//     onUnhandledRequest: 'warn', // 對未處理的請求發出警告
//   });
// });

// 在每個測試後重置處理器，確保測試間不互相影響
// afterEach(() => {
//   server.resetHandlers();
// });

// 在所有測試結束後關閉 MSW 服務器
// afterAll(() => {
//   server.close();
// });

// Mock NextResponse
global.NextResponse = {
  json: (data, options = {}) => {
    return new global.Response(JSON.stringify(data), {
      status: options.status || 200,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
  },
  redirect: (url, status = 302) => {
    return new global.Response(null, {
      status,
      headers: {
        'Location': url
      }
    })
  }
}

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock useSessionAvailability hook to prevent act() warnings
jest.mock('@/hooks/useSessionAvailability', () => ({
  useSessionAvailability: jest.fn(() => ({
    availability: [
      {
        sessionTime: '台北 07/20（日）13:20',
        maxCapacity: 10,
        registeredCount: 5,
        availableSpots: 5,
        isAvailable: true,
        showAvailability: false
      },
      {
        sessionTime: '台北 07/20（日）15:20',
        maxCapacity: 10,
        registeredCount: 8,
        availableSpots: 2,
        isAvailable: true,
        showAvailability: true
      },
      {
        sessionTime: '台中 07/18（五）19:20',
        maxCapacity: 8,
        registeredCount: 3,
        availableSpots: 5,
        isAvailable: true,
        showAvailability: false
      }
    ],
    loading: false,
    error: null,
    refetch: jest.fn()
  })),
  isSessionAvailable: (availability, sessionName) => {
    const session = availability.find(s => s.sessionTime === sessionName);
    return session ? session.isAvailable : true;
  },
  getAvailabilityText: (availability, sessionName) => {
    const session = availability.find(s => s.sessionTime === sessionName);
    if (!session) return '';
    if (!session.isAvailable) return '已額滿';
    if (session.showAvailability) return `剩餘 ${session.availableSpots} 名額`;
    return '';
  }
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}
