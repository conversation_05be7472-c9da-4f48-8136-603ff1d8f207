#!/bin/bash

# 測試執行腳本
# 使用方法: ./scripts/run-tests.sh [test-type]

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# 檢查 Node.js 版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安裝"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "需要 Node.js 18 或更高版本，當前版本: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js 版本檢查通過: $(node -v)"
}

# 檢查環境變數
check_env_vars() {
    print_header "檢查環境變數"
    
    if [ ! -f ".env.local" ] && [ ! -f ".env.test" ]; then
        print_warning "未找到環境變數檔案，將使用預設值"
    else
        print_success "環境變數檔案存在"
    fi
}

# 安裝依賴
install_dependencies() {
    print_header "安裝依賴"
    
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules 不存在，執行 npm install"
        npm install
    else
        print_success "依賴已安裝"
    fi
}

# 執行 ESLint
run_lint() {
    print_header "執行 ESLint"
    
    if npm run lint; then
        print_success "ESLint 檢查通過"
    else
        print_error "ESLint 檢查失敗"
        exit 1
    fi
}

# 執行單元測試
run_unit_tests() {
    print_header "執行單元測試"
    
    if npm run test:unit; then
        print_success "單元測試通過"
    else
        print_error "單元測試失敗"
        exit 1
    fi
}

# 執行整合測試
run_integration_tests() {
    print_header "執行整合測試"
    
    if npm run test:integration; then
        print_success "整合測試通過"
    else
        print_error "整合測試失敗"
        exit 1
    fi
}

# 執行 E2E 測試
run_e2e_tests() {
    print_header "執行 E2E 測試"
    
    # 檢查 Playwright 是否已安裝
    if ! npx playwright --version &> /dev/null; then
        print_warning "安裝 Playwright 瀏覽器"
        npx playwright install
    fi
    
    # 啟動開發伺服器
    print_warning "啟動開發伺服器"
    npm run dev &
    DEV_SERVER_PID=$!
    
    # 等待伺服器啟動
    sleep 10
    
    # 執行 E2E 測試
    if npm run test:e2e; then
        print_success "E2E 測試通過"
    else
        print_error "E2E 測試失敗"
        kill $DEV_SERVER_PID
        exit 1
    fi
    
    # 關閉開發伺服器
    kill $DEV_SERVER_PID
}

# 生成測試覆蓋率報告
generate_coverage() {
    print_header "生成測試覆蓋率報告"
    
    if npm run test:coverage; then
        print_success "測試覆蓋率報告已生成"
        print_warning "查看報告: open coverage/lcov-report/index.html"
    else
        print_error "測試覆蓋率報告生成失敗"
        exit 1
    fi
}

# 主函數
main() {
    local test_type=${1:-"all"}
    
    print_header "Pangea Website 自動化測試"
    echo "測試類型: $test_type"
    echo ""
    
    # 基本檢查
    check_node_version
    check_env_vars
    install_dependencies
    
    case $test_type in
        "lint")
            run_lint
            ;;
        "unit")
            run_lint
            run_unit_tests
            ;;
        "integration")
            run_lint
            run_integration_tests
            ;;
        "e2e")
            run_lint
            run_e2e_tests
            ;;
        "coverage")
            run_lint
            generate_coverage
            ;;
        "all")
            run_lint
            run_unit_tests
            run_integration_tests
            run_e2e_tests
            generate_coverage
            ;;
        *)
            print_error "未知的測試類型: $test_type"
            echo "可用的測試類型: lint, unit, integration, e2e, coverage, all"
            exit 1
            ;;
    esac
    
    print_success "所有測試完成！"
}

# 執行主函數
main "$@"
