// Jest setup for Node.js environment (API tests)

// Mock Next.js navigation for Node.js environment
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock environment variables for testing
process.env.APP_ENVIRONMENT = 'sandbox'
process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000'
process.env.PAYUNI_NOTIFY_URL = 'https://example.com/webhook'

// Mock console methods to reduce test noise
const originalConsoleLog = console.log
const originalConsoleError = console.error

beforeAll(() => {
  console.log = jest.fn()
  console.error = jest.fn()
})

afterAll(() => {
  console.log = originalConsoleLog
  console.error = originalConsoleError
})

// Global test utilities for API tests
global.createMockRequest = (url, options = {}) => {
  return new Request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  })
}

global.createMockFormData = (data) => {
  const formData = new FormData()
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value)
  })
  return formData
}

// Mock fetch for API tests
global.fetch = jest.fn()

// Setup for API route testing
beforeEach(() => {
  jest.clearAllMocks()
  
  // Reset environment variables
  process.env.APP_ENVIRONMENT = 'sandbox'
  process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000'
  process.env.PAYUNI_NOTIFY_URL = 'https://example.com/webhook'
})
