import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { generatePageMetadata } from '@/lib/seo-utils';

export const metadata: Metadata = generatePageMetadata('thankYou');

export default function ThankYouPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* 左側：文字內容 */}
          <div className="text-center lg:text-left order-1 lg:order-1">
            {/* 主標題 */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 lg:mb-8" style={{ color: '#2b354d' }}>
              感謝您的預約！
            </h1>

            {/* 說明文字 */}
            <div className="space-y-4 mb-8 lg:mb-10">
              <p className="text-lg md:text-xl leading-relaxed" style={{ color: '#2b354d', opacity: 0.8 }}>
                我們將盡快與您聯繫，別忘了到信箱確認您的預約
              </p>
              <p className="text-lg md:text-xl leading-relaxed" style={{ color: '#2b354d', opacity: 0.8 }}>
                若遲未收到可主動聯繫 <span style={{ color: '#2b354d', fontWeight: '600' }}><EMAIL></span>
              </p>
              <p className="text-lg md:text-xl leading-relaxed" style={{ color: '#2b354d', opacity: 0.8 }}>
                或至垃圾信件查詢
              </p>
            </div>

            {/* CTA 按鈕 */}
            <div className="flex justify-center lg:justify-start">
              <Button
                asChild
                size="lg"
                className="px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
                style={{ 
                  backgroundColor: '#2b354d', 
                  color: '#ffffff',
                  border: 'none'
                }}
              >
                <Link href="/">
                  回到首頁
                </Link>
              </Button>
            </div>
          </div>

          {/* 右側：動畫區域 */}
          <div className="flex justify-center lg:justify-end order-2 lg:order-2">
            <div className="w-full max-w-md lg:max-w-lg">
              {/* GIF 動畫 */}
              <div className="aspect-square bg-slate-100 rounded-2xl flex items-center justify-center overflow-hidden relative">
                <Image
                  src="/images/thank-you-animation.gif"
                  alt="感謝預約動畫"
                  fill
                  className="object-contain"
                  unoptimized
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
