'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface UsePageTransitionOptions {
  delay?: number; // 延遲顯示載入指示器的時間（毫秒）
}

interface UsePageTransitionReturn {
  isNavigating: boolean;
  navigate: (url: string) => void;
}

/**
 * 頁面轉換載入狀態管理 Hook
 * 提供平滑的頁面轉換體驗
 */
export function usePageTransition({
  delay = 150
}: UsePageTransitionOptions = {}): UsePageTransitionReturn {
  const [isNavigating, setIsNavigating] = useState(false);
  const router = useRouter();

  const navigate = (url: string) => {
    // 設置載入狀態
    setIsNavigating(true);
    
    // 延遲一小段時間再開始導航，讓載入指示器有時間顯示
    setTimeout(() => {
      router.push(url);
    }, delay);
  };

  // 監聽路由變化，重置載入狀態
  useEffect(() => {
    const handleRouteChange = () => {
      setIsNavigating(false);
    };

    // 監聽 popstate 事件（瀏覽器前進/後退）
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // 在組件卸載時重置狀態
  useEffect(() => {
    return () => {
      setIsNavigating(false);
    };
  }, []);

  return {
    isNavigating,
    navigate
  };
}

/**
 * 全域載入狀態管理 Hook
 * 用於顯示全域載入指示器
 */
export function useGlobalLoading() {
  const [isLoading, setIsLoading] = useState(false);

  const startLoading = () => setIsLoading(true);
  const stopLoading = () => setIsLoading(false);

  return {
    isLoading,
    startLoading,
    stopLoading
  };
}
