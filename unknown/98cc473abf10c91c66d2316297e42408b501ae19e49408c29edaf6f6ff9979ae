import { MetadataRoute } from 'next';
import { SITE_CONFIG } from '@/config/seo-config';
import { getServerBlogPosts } from '@/lib/blog-server';
import { getServerWatches } from '@/lib/watches-server';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = SITE_CONFIG.url;
  
  // 靜態頁面
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/pre-owned-watches`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/movement-assembling-booking`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/pangea-booking`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/support`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
  ];

  // 動態頁面 - 部落格文章
  let blogPages: MetadataRoute.Sitemap = [];
  try {
    const posts = await getServerBlogPosts();
    blogPages = posts.map((post) => ({
      url: `${baseUrl}/post/${post.slug}`,
      lastModified: new Date(post.publishDate),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }));
  } catch (error) {
    console.error('Error generating blog sitemap:', error);
  }

  // 動態頁面 - 手錶詳情
  let watchPages: MetadataRoute.Sitemap = [];
  try {
    const watches = await getServerWatches();
    watchPages = watches.map((watch) => ({
      url: `${baseUrl}/pre-owned-watches-details/${watch.seoSlug || watch.id}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }));
  } catch (error) {
    console.error('Error generating watch sitemap:', error);
  }

  return [...staticPages, ...blogPages, ...watchPages];
}
