import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface CTAButton {
  text: string;
  link: string;
  primary?: boolean;
}

interface CTASectionProps {
  title: string;
  subtitle?: string;
  description?: string;
  buttons: CTAButton[];
  backgroundImage?: string;
}

const CTASection = ({
  title,
  subtitle,
  description,
  buttons,
  backgroundImage
}: CTASectionProps) => {
  return (
    <section className="relative py-32 bg-gradient-to-br from-primary/98 via-primary to-primary/95 text-primary-foreground overflow-hidden">
      {/* Background Image */}
      {backgroundImage && (
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}

      {/* Enhanced Background Patterns */}
      <div className="absolute inset-0 bg-gradient-to-r from-accent/15 via-accent/10 to-accent/5" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_70%)]" />

      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-accent/20 rounded-full blur-xl" />
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-accent/15 rounded-full blur-2xl" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Main Title */}
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 bg-gradient-to-r from-primary-foreground via-primary-foreground/95 to-primary-foreground/80 bg-clip-text text-transparent leading-tight">
            {title}
          </h2>

          {/* Subtitle */}
          {subtitle && (
            <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-light mb-4 sm:mb-6 text-primary-foreground/95 leading-relaxed">
              {subtitle}
            </h3>
          )}

          {/* Description */}
          {description && (
            <p className="text-base sm:text-lg md:text-xl opacity-90 mb-12 sm:mb-16 max-w-4xl mx-auto leading-relaxed px-4">
              {description}
            </p>
          )}

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center px-4">
            {buttons.map((button, index) => (
              <Button
                key={index}
                asChild
                size="lg"
                variant={button.primary ? "default" : "outline"}
                className={cn(
                  "font-bold py-4 sm:py-6 px-6 sm:px-10 rounded-full text-base sm:text-lg transition-all duration-500 transform hover:scale-110",
                  "shadow-2xl hover:shadow-3xl",
                  button.primary
                    ? "bg-gradient-to-r from-accent via-orange-500 to-orange-600 hover:from-accent/90 hover:via-orange-400 hover:to-orange-500 text-white border-0 hover:shadow-accent/30"
                    : "border-2 sm:border-3 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary hover:shadow-primary-foreground/20 backdrop-blur-sm"
                )}
              >
                <a href={button.link} className="flex items-center gap-2">
                  {button.text}
                  {button.primary && (
                    <svg className="w-6 h-6 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  )}
                </a>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
