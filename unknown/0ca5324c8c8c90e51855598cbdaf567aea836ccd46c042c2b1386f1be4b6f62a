// 伺服器端部落格資料獲取函數
// 用於 generateMetadata 和伺服器組件

import { getBlogSheetData, getSheetData, getBlogDetailSheetData } from '@/lib/google-sheets';
import { transformBlogData, type BlogPost } from '@/types/blog';

// 部落格文章的 Google Sheets 範圍
const POSSIBLE_BLOG_RANGES = [
  'Blog!A:L',           // 英文名稱
  '部落格文章!A:L',      // 中文名稱
  'Sheet1!A:L',         // 預設名稱
  'A:L'                 // 不指定工作表
];

// 嘗試讀取資料的輔助函數
async function tryReadBlogData(): Promise<string[][] | null> {
  for (const range of POSSIBLE_BLOG_RANGES) {
    try {
      const data = await getBlogSheetData(range);
      if (data && data.length > 0) {
        return data;
      }
    } catch {
      // 繼續嘗試下一個範圍
    }
  }

  // 如果 getBlogSheetData 都失敗，嘗試使用通用的 getSheetData
  for (const range of POSSIBLE_BLOG_RANGES) {
    try {
      const data = await getSheetData(range);
      if (data && data.length > 0) {
        return data;
      }
    } catch {
      // 繼續嘗試下一個範圍
    }
  }

  return null;
}

// 伺服器端獲取單篇部落格文章（優化版本）
export async function getServerBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    // 使用優化的讀取函數
    const rawData = await getBlogDetailSheetData(slug);

    if (!rawData || rawData.length === 0) {
      console.error('無法讀取部落格資料');
      return null;
    }

    // 轉換資料 - 遍歷所有行並轉換
    const posts: BlogPost[] = [];
    for (let i = 1; i < rawData.length; i++) { // 跳過標題行
      try {
        const post = transformBlogData(rawData[i], i);
        posts.push(post);
      } catch (error) {
        console.warn(`轉換第 ${i} 行資料失敗:`, error);
      }
    }

    // 尋找指定的文章
    const post = posts.find(p => p.slug === slug || p.seoSlug === slug);

    if (!post) {
      console.error(`找不到文章: ${slug}`);
      return null;
    }

    return post;
  } catch (error) {
    console.error('獲取部落格文章失敗:', error);
    // 如果優化版本失敗，回退到原始方法
    console.log('回退到原始讀取方法...');
    return getServerBlogPostFallback(slug);
  }
}

// 回退方法：使用原始的讀取方式
async function getServerBlogPostFallback(slug: string): Promise<BlogPost | null> {
  try {
    const rawData = await tryReadBlogData();

    if (!rawData || rawData.length === 0) {
      console.error('無法讀取部落格資料');
      return null;
    }

    // 轉換資料 - 遍歷所有行並轉換
    const posts: BlogPost[] = [];
    for (let i = 1; i < rawData.length; i++) { // 跳過標題行
      try {
        const post = transformBlogData(rawData[i], i);
        posts.push(post);
      } catch (error) {
        console.warn(`轉換第 ${i} 行資料失敗:`, error);
      }
    }

    // 尋找指定的文章
    const post = posts.find(p => p.slug === slug || p.seoSlug === slug);

    if (!post) {
      console.error(`找不到文章: ${slug}`);
      return null;
    }

    return post;
  } catch (error) {
    console.error('獲取部落格文章失敗:', error);
    return null;
  }
}

// 伺服器端獲取所有部落格文章（用於 sitemap 等）
export async function getServerBlogPosts(): Promise<BlogPost[]> {
  try {
    const rawData = await tryReadBlogData();
    
    if (!rawData || rawData.length === 0) {
      console.error('無法讀取部落格資料');
      return [];
    }

    // 轉換資料 - 遍歷所有行並轉換
    const posts: BlogPost[] = [];
    for (let i = 1; i < rawData.length; i++) { // 跳過標題行
      try {
        const post = transformBlogData(rawData[i], i);
        posts.push(post);
      } catch (error) {
        console.warn(`轉換第 ${i} 行資料失敗:`, error);
      }
    }

    return posts;
  } catch (error) {
    console.error('獲取部落格文章列表失敗:', error);
    return [];
  }
}
