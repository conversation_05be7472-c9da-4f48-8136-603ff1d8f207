import { Metadata } from 'next';
import { Suspense } from 'react';
import { getServerBlogPost } from '@/lib/blog-server';
import { generateDynamicMetadata } from '@/lib/seo-utils';
import BlogDetailContent from '@/components/BlogDetailContent';
import BlogDetailSkeleton from '@/components/BlogDetailSkeleton';

// 生成 metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const post = await getServerBlogPost(resolvedParams.slug);

  if (!post) {
    return {
      title: '文章不存在',
      description: '找不到指定的文章',
    };
  }

  // 使用簡化的動態 metadata 生成
  const title = post.seoTitle || `${post.title} - Weaven Blog`;
  const description = post.seoDescription || post.title;
  const image = post.socialImage || post.thumbnail;

  return generateDynamicMetadata(title, description, image, `/blog/${post.slug}`);
}

const BlogPostPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const resolvedParams = await params;

  return (
    <Suspense fallback={<BlogDetailSkeleton />}>
      <BlogDetailContent slug={resolvedParams.slug} />
    </Suspense>
  );
};

export default BlogPostPage;
