import Link from 'next/link';
import Image from 'next/image';
import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { generatePageMetadata } from '@/lib/seo-utils';

export const metadata: Metadata = generatePageMetadata('notFound');

export default function NotFound() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* 左側：文字內容（桌面版）/ 上方：文字內容（手機版） */}
          <div className="text-center lg:text-left order-1 lg:order-1">
            {/* 主標題 */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 lg:mb-8" style={{ color: '#2b354d' }}>
              您所尋找的頁面不存在
            </h1>

            {/* 副標題 */}
            <p className="text-lg md:text-xl mb-8 lg:mb-10" style={{ color: '#2b354d', opacity: 0.7 }}>
              請嘗試回到首頁或是與我們聯繫
            </p>

            {/* 按鈕 */}
            <div className="flex justify-center lg:justify-start">
              <Button
                asChild
                className="h-12 px-8 text-base font-medium transition-all duration-200 rounded-lg"
                style={{
                  backgroundColor: '#2b354d',
                  color: 'white',
                  border: 'none'
                }}
              >
                <Link href="/">
                  回到首頁
                </Link>
              </Button>
            </div>
          </div>

          {/* 右側：GIF 圖片（桌面版）/ 下方：GIF 圖片（手機版） */}
          <div className="flex justify-center lg:justify-end order-2 lg:order-2">
            <div className="w-full max-w-md lg:max-w-lg">
              {/* 這裡放置您的 404 GIF 圖片 */}
              <div className="relative w-full aspect-square">
                <Image
                  src="/images/404-animation.gif"
                  alt="404 頁面找不到"
                  fill
                  className="object-contain"
                  priority
                  unoptimized // 對於 GIF 動畫，建議使用 unoptimized
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
