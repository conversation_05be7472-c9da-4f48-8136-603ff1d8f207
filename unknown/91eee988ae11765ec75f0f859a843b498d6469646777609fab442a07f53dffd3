// 手錶資料類型定義，對應 Google Sheets 欄位結構

export interface WatchData {
  // Google Sheets 原始欄位
  productName: string;           // Product Name
  brand: string;                 // Brand
  serialNumber: string;          // Serial Number
  boxAndPaper: string;           // Box & Paper
  listingDescription: string;    // Listing Description
  year: string;                  // Year
  image: string;                 // Image (主圖片 URL)
  cta: string;                   // CTA (粉專連結)
  thumbnail: string;             // Thumbnail (縮圖 URL)
  price: string;                 // Price
  movement: string;              // Movement
  dialType: string;              // Dial Type
  caseSize: string;              // Case Size(mm)
  lugToLug: string;              // Lug to lug
  condition: string;             // Condition
  // SEO 欄位
  seoSlug: string;               // SEO:Slug (網址路徑)
  seoTitle: string;              // SEO:Title (meta 標題)
  seoDescription: string;        // SEO:Description (meta 描述)
  // 社群分享欄位
  socialImage: string;           // Social:Image (社群分享縮圖)
  socialTitle: string;           // Social:Title (社群分享標題)
  socialDescription: string;     // Social:Description (社群分享描述)
}

// 前端使用的手錶類型（處理過的資料）
export interface Watch {
  id: string;                    // 由 row index 或 serial number 生成
  productName: string;
  brand: string;
  serialNumber: string;
  boxAndPaper: string;
  listingDescription: string;
  year: string;
  image: string;
  cta: string;
  thumbnail: string;
  price: number;                 // 轉換為數字
  movement: string;
  dialType: string;
  caseSize: string;
  lugToLug: string;
  condition: string;
  // SEO 欄位
  seoSlug: string;               // SEO:Slug (網址路徑)
  seoTitle: string;              // SEO:Title (meta 標題)
  seoDescription: string;        // SEO:Description (meta 描述)
  // 社群分享欄位
  socialImage: string;           // Social:Image (社群分享縮圖)
  socialTitle: string;           // Social:Title (社群分享標題)
  socialDescription: string;     // Social:Description (社群分享描述)
}

// 列表頁面使用的簡化手錶類型
export interface WatchListItem {
  id: string;
  productName: string;
  brand: string;
  thumbnail: string;
  price: number;
  seoSlug: string;               // SEO:Slug (用於生成 URL)
}

// API 響應類型
export interface WatchesResponse {
  watches: WatchListItem[];
  brands: string[];
  total: number;
  hasMore: boolean;
  page?: number;
  pageSize?: number;
}

// Google Sheets 欄位對應
export const WATCH_SHEET_COLUMNS = {
  PRODUCT_NAME: 'A',      // Product Name
  BRAND: 'B',             // Brand
  SERIAL_NUMBER: 'C',     // Serial Number
  BOX_AND_PAPER: 'D',     // Box & Paper
  LISTING_DESCRIPTION: 'E', // Listing Description
  YEAR: 'F',              // Year
  IMAGE: 'G',             // Image
  CTA: 'H',               // CTA
  THUMBNAIL: 'I',         // Thumbnail
  PRICE: 'J',             // Price
  MOVEMENT: 'K',          // Movement
  DIAL_TYPE: 'L',         // Dial Type
  CASE_SIZE: 'M',         // Case Size(mm)
  LUG_TO_LUG: 'N',        // Lug to lug
  CONDITION: 'O',         // Condition
  SEO_SLUG: 'P',          // SEO:Slug
  SEO_TITLE: 'Q',         // SEO:Title
  SEO_DESCRIPTION: 'R',   // SEO:Description
  SOCIAL_IMAGE: 'S',      // Social:Image
  SOCIAL_TITLE: 'T',      // Social:Title
  SOCIAL_DESCRIPTION: 'U', // Social:Description
} as const;

// 將 Google Sheets 原始資料轉換為 Watch 物件
export function transformWatchData(rawData: string[], index: number): Watch {
  const [
    productName = '',
    brand = '',
    serialNumber = '',
    boxAndPaper = '',
    listingDescription = '',
    year = '',
    image = '',
    cta = '',
    thumbnail = '',
    price = '0',
    movement = '',
    dialType = '',
    caseSize = '',
    lugToLug = '',
    condition = '',
    seoSlug = '',
    seoTitle = '',
    seoDescription = '',
    socialImage = '',
    socialTitle = '',
    socialDescription = ''
  ] = rawData;

  // 使用 row index 作為唯一 ID，Serial Number 不需要唯一
  const uniqueId = `watch-${index}`;

  return {
    id: uniqueId,
    productName,
    brand,
    serialNumber,
    boxAndPaper,
    listingDescription,
    year,
    image,
    cta,
    thumbnail,
    price: parseFloat(price.replace(/[^0-9.-]+/g, '')) || 0, // 移除貨幣符號並轉換為數字
    movement,
    dialType,
    caseSize,
    lugToLug,
    condition,
    // SEO 欄位
    seoSlug,
    seoTitle,
    seoDescription,
    // 社群分享欄位
    socialImage,
    socialTitle,
    socialDescription
  };
}

// 將 Watch 轉換為 WatchListItem
export function toWatchListItem(watch: Watch): WatchListItem {
  return {
    id: watch.id,
    productName: watch.productName,
    brand: watch.brand,
    thumbnail: watch.thumbnail,
    price: watch.price,
    seoSlug: watch.seoSlug
  };
}

// 將優化的 Google Sheets 資料（只包含列表需要的欄位）直接轉換為 WatchListItem
export function transformWatchListData(rawData: string[], index: number): WatchListItem {
  const [
    productName = '',  // A欄
    brand = '',        // B欄
    thumbnail = '',    // I欄
    price = '0',       // J欄
    seoSlug = ''       // P欄
  ] = rawData;

  // 使用 row index 作為唯一 ID
  const uniqueId = `watch-${index}`;

  return {
    id: uniqueId,
    productName,
    brand,
    thumbnail,
    price: parseFloat(price.replace(/[^0-9.-]+/g, '')) || 0, // 移除貨幣符號並轉換為數字
    seoSlug
  };
}

// 品牌列表（用於篩選器）
export function getUniqueBrands(watches: Watch[]): string[] {
  const brands = watches.map(watch => watch.brand).filter(Boolean);
  return [...new Set(brands)].sort();
}
