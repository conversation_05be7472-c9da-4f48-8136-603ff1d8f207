import Image from "next/image";

interface FullscreenImageProps {
  image: string;
  alt: string;
}

const FullscreenImage = ({
  image,
  alt
}: FullscreenImageProps) => {
  return (
    <section className="w-full">
      <div className="w-full">
        <Image
          src={image}
          alt={alt}
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto block"
          priority
        />
      </div>
    </section>
  );
};

export default FullscreenImage;
