import { NextResponse } from 'next/server';

/**
 * GET /api/performance/test
 * 效能測試 API - 測試各個端點的回應時間
 */
export async function GET() {
  const results: Array<{
    endpoint: string;
    type: string;
    responseTime: number;
    dataSize: number;
    status: 'success' | 'error';
    error?: string;
  }> = [];

  const testEndpoints = [
    { url: '/api/blog', name: '部落格列表 (已優化)', type: 'optimized' },
    { url: '/api/blog/list', name: '部落格列表 (專用優化)', type: 'list' },
    { url: '/api/pre-owned-watches', name: '手錶列表 (已優化)', type: 'optimized' },
    { url: '/api/support', name: '常見問題 (已優化)', type: 'optimized' }
  ];

  const baseUrl = process.env.VERCEL_URL 
    ? `https://${process.env.VERCEL_URL}` 
    : 'http://localhost:3000';

  for (const endpoint of testEndpoints) {
    try {
      const startTime = Date.now();
      
      const response = await fetch(`${baseUrl}${endpoint.url}`, {
        headers: {
          'User-Agent': 'Performance-Test'
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const data = await response.json();
      const dataSize = JSON.stringify(data).length;

      results.push({
        endpoint: endpoint.name,
        type: endpoint.type,
        responseTime,
        dataSize,
        status: 'success'
      });
      
    } catch (error) {
      results.push({
        endpoint: endpoint.name,
        type: endpoint.type,
        responseTime: 0,
        dataSize: 0,
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // 計算統計資訊
  const successfulTests = results.filter(r => r.status === 'success');
  const avgResponseTime = successfulTests.length > 0 
    ? successfulTests.reduce((sum, r) => sum + r.responseTime, 0) / successfulTests.length 
    : 0;
  
  const listEndpoints = successfulTests.filter(r => r.type === 'list');
  const optimizedEndpoints = successfulTests.filter(r => r.type === 'optimized');

  const avgListTime = listEndpoints.length > 0
    ? listEndpoints.reduce((sum, r) => sum + r.responseTime, 0) / listEndpoints.length
    : 0;

  const avgOptimizedTime = optimizedEndpoints.length > 0
    ? optimizedEndpoints.reduce((sum, r) => sum + r.responseTime, 0) / optimizedEndpoints.length
    : 0;

  return NextResponse.json({
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: results.length,
      successfulTests: successfulTests.length,
      averageResponseTime: Math.round(avgResponseTime),
      optimizedEndpointsAvg: Math.round(avgOptimizedTime),
      specializedEndpointsAvg: Math.round(avgListTime)
    },
    results,
    recommendations: [
      avgResponseTime > 1000 ? '⚠️ 平均回應時間超過 1 秒，建議檢查網路連線或 Google Sheets API 狀態' : '✅ 回應時間正常',
      avgOptimizedTime < 200 ? '⚡ API 回應速度優秀' : '🔧 可考慮進一步優化'
    ]
  });
}
