import { NextRequest, NextResponse } from 'next/server';
import { queryPayUniOrder, convertTradeStatus, convertPaymentType, getOverallPaymentStatus } from '@/lib/payuni';
import { getSheetData } from '@/lib/google-sheets';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderNo: string }> }
) {
  try {
    const { orderNo } = await params;
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!orderNo) {
      return NextResponse.json(
        { error: '缺少訂單號碼' },
        { status: 400 }
      );
    }

    console.log(`🔍 開始查詢訂單: ${orderNo}${email ? `, Email: ${email}` : ''}`);

    // 首先嘗試從 Google Sheets 查詢訂單（用於新建立的訂單）
    try {
      console.log('📊 嘗試從 Google Sheets 查詢訂單...');
      const sheetData = await getSheetData('工作表1!A:AD');

      if (sheetData && sheetData.length > 0) {
        // 尋找對應的訂單 (訂單號碼在 V 欄，索引 21)
        const orderRow = sheetData.find(row => row[21] === orderNo);

        if (orderRow) {
          console.log('✅ 在 Google Sheets 中找到訂單');

          // 解析訂單資料
          const orderData = {
            orderNo: orderRow[21] || orderNo,
            name: orderRow[4] || '',
            email: orderRow[5] || '',
            eventName: '錶匠體驗機芯拆解',
            eventPrice: parseInt(orderRow[23]) || 1500,
            sessionTimes: orderRow[1] ? orderRow[1].split(', ') : [],
            participationType: orderRow[3] || '',
            submittedAt: orderRow[0] || '',
          };

          return NextResponse.json(orderData);
        }
      }
    } catch (sheetError) {
      console.error('❌ Google Sheets 查詢失敗，嘗試 PayUni API:', sheetError);
    }

    // 如果 Google Sheets 中找不到，再嘗試 PayUni API
    console.log('📡 使用 PayUni API 查詢訂單...');

    // 建立查詢參數
    const queryParams: { orderNo: string; email?: string } = { orderNo };
    if (email) {
      queryParams.email = email;
    }

    // 向 PayUni API 查詢訂單狀態
    const payuniData = await queryPayUniOrder(queryParams);

    if (!payuniData) {
      return NextResponse.json(
        { error: '找不到指定的訂單或查詢失敗' },
        { status: 404 }
      );
    }

    // 檢查查詢結果狀態
    if (payuniData.Status !== 'SUCCESS') {
      console.error(`❌ PayUni 查詢失敗: ${payuniData.Status} - ${payuniData.Message}`);
      return NextResponse.json(
        { error: '查詢訂單失敗，請確認訂單號碼是否正確' },
        { status: 404 }
      );
    }

    // 轉換 PayUni 資料為前端期望的格式
    const tradeStatus = String(payuniData.TradeStatus || '');
    const paymentType = String(payuniData.PaymentType || '');

    // 使用綜合狀態判斷函數，考慮退款狀態
    const paymentStatus = getOverallPaymentStatus(payuniData);

    // 格式化付款完成時間
    let paymentCompletedAt = '';
    if (payuniData.PaymentDay && tradeStatus === '1') {
      paymentCompletedAt = String(payuniData.PaymentDay);
    }

    // 建構回傳的訂單資料 (保持與前端期望的格式一致)
    const orderData = {
      orderNo: String(payuniData.MerTradeNo || orderNo),
      name: '', // PayUni 查詢結果中沒有姓名資訊
      email: '', // PayUni 查詢結果中沒有 Email 資訊
      phone: '', // PayUni 查詢結果中沒有電話資訊
      sessionTimes: [], // PayUni 查詢結果中沒有場次資訊
      participationType: '', // PayUni 查詢結果中沒有參與類型資訊
      eventPrice: parseInt(String(payuniData.TradeAmt || '0')),
      submittedAt: String(payuniData.CreateDay || ''),
      paymentStatus,
      paymentMethod: convertPaymentType(paymentType),
      paymentCompletedAt,
      payuniTradeNo: String(payuniData.TradeNo || ''),
      payuniPayNo: String(payuniData.OffPayNo || ''),
      notes: '',
      eventName: '錶匠體驗',
      // 新增 PayUni 查詢的額外資訊
      tradeStatus: convertTradeStatus(tradeStatus),
      gateway: String(payuniData.Gateway || ''),
      dataSource: 'PayUni API', // 標記資料來源
    };

    console.log(`✅ 成功查詢訂單 ${orderNo}:`, {
      paymentStatus: orderData.paymentStatus,
      tradeStatus: orderData.tradeStatus,
      paymentMethod: orderData.paymentMethod,
      amount: orderData.eventPrice
    });

    // 返回包含原始 PayUni 資料的完整回應
    return NextResponse.json({
      ...orderData,
      // 加入原始 PayUni API 回應，供 PayUniQueryResult 組件使用
      payuniRawData: payuniData
    });

  } catch (error) {
    console.error('處理訂單查詢請求時發生錯誤:', error);
    return NextResponse.json(
      { error: '伺服器錯誤，請稍後再試' },
      { status: 500 }
    );
  }
}
