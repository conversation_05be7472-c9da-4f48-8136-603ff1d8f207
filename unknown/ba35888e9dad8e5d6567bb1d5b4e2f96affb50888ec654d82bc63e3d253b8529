import { NextRequest, NextResponse } from 'next/server';

/**
 * GTM Proxy API Route
 * 代理 Google Tag Manager 的請求，避免廣告阻擋器干擾
 * 
 * 支援的路徑：
 * - /api/gtm-proxy/gtm.js?id=GTM-XXXXXXX
 * - /api/gtm-proxy/ns.html?id=GTM-XXXXXXX
 * - /api/gtm-proxy/gtag/js?id=GA_MEASUREMENT_ID
 */

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const url = new URL(request.url);
    
    // 建構目標 URL
    const targetPath = path.join('/');
    const targetUrl = `https://www.googletagmanager.com/${targetPath}${url.search}`;
    
    // 開發環境下記錄請求
    if (process.env.NODE_ENV === 'development') {
      console.log(`[GTM Proxy] ${request.method} ${targetUrl}`);
    }
    
    // 準備請求標頭
    const headers: HeadersInit = {
      'User-Agent': request.headers.get('User-Agent') || 'Mozilla/5.0 (compatible; GTM-Proxy)',
      'Accept': request.headers.get('Accept') || '*/*',
      'Accept-Language': request.headers.get('Accept-Language') || 'en-US,en;q=0.9',
      'Accept-Encoding': request.headers.get('Accept-Encoding') || 'gzip, deflate, br',
    };
    
    // 如果有 Referer，也傳遞過去
    const referer = request.headers.get('Referer');
    if (referer) {
      headers['Referer'] = referer;
    }
    
    // 發送請求到 Google Tag Manager
    const response = await fetch(targetUrl, {
      method: request.method,
      headers,
      // 設定超時時間
      signal: AbortSignal.timeout(10000), // 10 秒超時
    });
    
    if (!response.ok) {
      console.error(`[GTM Proxy] Error: ${response.status} ${response.statusText}`);
      return new NextResponse(`GTM Proxy Error: ${response.statusText}`, {
        status: response.status,
      });
    }
    
    // 準備回應標頭
    const responseHeaders = new Headers();
    
    // 複製重要的回應標頭
    const importantHeaders = [
      'content-type',
      'content-length',
      'content-encoding',
      'etag',
      'last-modified',
    ];
    
    importantHeaders.forEach(headerName => {
      const headerValue = response.headers.get(headerName);
      if (headerValue) {
        responseHeaders.set(headerName, headerValue);
      }
    });
    
    // 設定快取標頭
    if (targetPath.includes('gtm.js') || targetPath.includes('gtag/js')) {
      // JavaScript 檔案快取 1 小時
      responseHeaders.set('Cache-Control', 'public, max-age=3600, s-maxage=3600');
    } else if (targetPath.includes('ns.html')) {
      // HTML 檔案快取 30 分鐘
      responseHeaders.set('Cache-Control', 'public, max-age=1800, s-maxage=1800');
    } else {
      // 其他檔案快取 10 分鐘
      responseHeaders.set('Cache-Control', 'public, max-age=600, s-maxage=600');
    }
    
    // 設定 CORS 標頭（如果需要）
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, User-Agent, Referer');
    
    // 安全標頭
    responseHeaders.set('X-Content-Type-Options', 'nosniff');
    responseHeaders.set('X-Frame-Options', 'DENY');
    
    return new NextResponse(response.body, {
      status: response.status,
      headers: responseHeaders,
    });
    
  } catch (error) {
    console.error('[GTM Proxy] Unexpected error:', error);
    
    // 如果是超時錯誤
    if (error instanceof Error && error.name === 'TimeoutError') {
      return new NextResponse('GTM Proxy Timeout', { status: 504 });
    }
    
    return new NextResponse('GTM Proxy Internal Error', { status: 500 });
  }
}

// 支援 HEAD 請求
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  // HEAD 請求與 GET 相同，但不返回 body
  const response = await GET(request, { params });
  return new NextResponse(null, {
    status: response.status,
    headers: response.headers,
  });
}

// 支援 OPTIONS 請求（CORS 預檢）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, User-Agent, Referer',
      'Access-Control-Max-Age': '86400', // 24 小時
    },
  });
}
