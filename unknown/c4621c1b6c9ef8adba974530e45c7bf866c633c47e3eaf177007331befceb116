"use client";

import { useState, useRef, useEffect, FormEvent } from 'react';
import { PAYUNI_CONFIG } from '@/config/payuni-config';

// Define the type for a single event, adding price
export type Event = {
  id: string;
  name: string;
  date: string;
  location: string;
  spotsLeft: number;
  isFull: boolean;
  price: number;
};

// Define the type for the payment data from our backend
interface PayUniPaymentData {
  MerID: string;
  TradeInfo: string;
  TradeSha: string;
}

const EventCard = ({ event }: { event: Event }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [paymentData, setPaymentData] = useState<PayUniPaymentData | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // This effect triggers when paymentData is received from the backend
  useEffect(() => {
    if (paymentData && formRef.current) {
      // The data is ready, submit the form to redirect to PayUni
      formRef.current.submit();
    }
  }, [paymentData]);

  const handleRegister = async (e: FormEvent) => {
    e.preventDefault();
    if (!userName || !userEmail) {
      alert('請輸入姓名與 Email');
      return;
    }

    setIsLoading(true);
    
    // 在開發環境中模擬成功回應
    if (process.env.NODE_ENV === 'development') {
      console.log('開發模式：模擬報名成功');
      console.log('活動:', event.name);
      console.log('姓名:', userName);
      console.log('Email:', userEmail);
      console.log('費用:', event.price);
      
      // 模擬 API 延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 顯示成功訊息
      alert('開發模式：報名成功！\n\n' +
            `活動：${event.name}\n` +
            `姓名：${userName}\n` +
            `Email：${userEmail}\n` +
            `費用：NT$ ${event.price}\n\n` +
            '在正式環境中，您將被導向到付款頁面。');
      
      setIsLoading(false);
      return;
    }

    // 正式環境中的實際 API 呼叫
    try {
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: event.id,
          eventName: event.name,
          eventPrice: event.price,
          userName,
          userEmail,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '報名失敗，請稍後再試');
      }

      const data: PayUniPaymentData = await response.json();
      // Set the payment data, which will trigger the useEffect to submit the form
      setPaymentData(data);

    } catch (error) {
      console.error(error);
      alert(error instanceof Error ? error.message : '發生未知錯誤');
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="border rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow flex flex-col h-full">
        {event.isFull ? (
          <div className="flex flex-col h-full">
            <div className="flex-grow">
              <h2 className="text-2xl font-bold mb-2 text-gray-500">{event.name}</h2>
              <p className="text-gray-500 mb-1">日期：{event.date}</p>
              <p className="text-gray-500 mb-4">地點：{event.location}</p>
            </div>
            <div className="mt-auto pt-4 text-center">
              <span className="text-red-500 font-semibold text-lg">已額滿</span>
            </div>
          </div>
        ) : (
          <form onSubmit={handleRegister} className="flex flex-col h-full">
            <div className="flex-grow">
              <h2 className="text-2xl font-bold mb-2">{event.name}</h2>
              <p className="text-gray-600 mb-1">日期：{event.date}</p>
              <p className="text-gray-600 mb-1">地點：{event.location}</p>
              <p className="text-gray-800 font-semibold mb-4">費用：NT$ {event.price}</p>
              <div className="mb-4">
                <label htmlFor={`name-${event.id}`} className="block text-sm font-medium text-gray-700">姓名</label>
                <input
                  type="text"
                  id={`name-${event.id}`}
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                  disabled={isLoading}
                />
              </div>
              <div>
                <label htmlFor={`email-${event.id}`} className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  id={`email-${event.id}`}
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                  disabled={isLoading}
                />
              </div>
            </div>
            <div className="flex justify-between items-center mt-auto pt-4">
              <span className="text-green-600 font-semibold">剩餘名額：{event.spotsLeft}</span>
              <button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? '處理中...' : '立即報名'}
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Hidden form for PayUni redirection */}
      <form
        ref={formRef}
        action={PAYUNI_CONFIG.getApiUrl()}
        method="POST"
        style={{ display: 'none' }}
      >
        <input type="text" name="MerID" value={paymentData?.MerID || ''} readOnly />
        <input type="text" name="TradeInfo" value={paymentData?.TradeInfo || ''} readOnly />
        <input type="text" name="TradeSha" value={paymentData?.TradeSha || ''} readOnly />
      </form>
    </>
  );
};

export default EventCard;
