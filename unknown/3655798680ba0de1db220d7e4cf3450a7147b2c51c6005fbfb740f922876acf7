'use client';

import { useEffect, useState } from 'react';

interface LoadingIndicatorProps {
  isLoading: boolean;
  delay?: number; // 延遲顯示的時間（毫秒）
  message?: string;
}

/**
 * 全域載入指示器組件
 * 在頁面轉換時顯示載入狀態
 */
export default function LoadingIndicator({ 
  isLoading, 
  delay = 200,
  message = '載入中...'
}: LoadingIndicatorProps) {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isLoading) {
      // 延遲顯示載入指示器，避免閃爍
      timeoutId = setTimeout(() => {
        setShouldShow(true);
      }, delay);
    } else {
      // 立即隱藏
      setShouldShow(false);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isLoading, delay]);

  if (!shouldShow) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        {/* 載入動畫 */}
        <div className="relative">
          <div className="w-12 h-12 border-4 border-gray-200 rounded-full animate-spin">
            <div className="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>
          </div>
        </div>
        
        {/* 載入訊息 */}
        <p className="text-gray-600 font-medium">{message}</p>
      </div>
    </div>
  );
}

/**
 * 頁面載入進度條組件
 * 顯示在頁面頂部的進度條
 */
export function LoadingProgressBar({ isLoading }: { isLoading: boolean }) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isLoading) {
      setProgress(0);
      
      // 模擬載入進度
      intervalId = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            return prev; // 停在 90%，等待實際載入完成
          }
          return prev + Math.random() * 10;
        });
      }, 100);
    } else {
      // 載入完成，快速到達 100% 然後隱藏
      setProgress(100);
      setTimeout(() => {
        setProgress(0);
      }, 200);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isLoading]);

  if (!isLoading && progress === 0) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div 
        className="h-1 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-200 ease-out"
        style={{ 
          width: `${progress}%`,
          opacity: isLoading ? 1 : 0
        }}
      />
    </div>
  );
}

/**
 * 簡單的載入旋轉器組件
 */
export function LoadingSpinner({ 
  size = 'md',
  color = 'blue'
}: {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'gray' | 'white';
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const colorClasses = {
    blue: 'border-blue-600',
    gray: 'border-gray-600',
    white: 'border-white'
  };

  return (
    <div className={`${sizeClasses[size]} border-2 border-gray-200 rounded-full animate-spin`}>
      <div className={`absolute top-0 left-0 ${sizeClasses[size]} border-2 border-transparent ${colorClasses[color]} border-t-current rounded-full animate-spin`}></div>
    </div>
  );
}
