import { Metadata } from 'next';
import { Suspense } from 'react';
import { getServerWatch } from '@/lib/watches-server';
import { generateDynamicMetadata } from '@/lib/seo-utils';
import WatchDetailContent from '@/components/WatchDetailContent';
import WatchDetailSkeleton from '@/components/WatchDetailSkeleton';

// 生成 metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const watch = await getServerWatch(resolvedParams.slug);

  if (!watch) {
    return {
      title: '手錶不存在',
      description: '找不到指定的手錶',
    };
  }

  // 使用簡化的動態 metadata 生成
  const title = watch.seoTitle || `${watch.brand} ${watch.productName} - 精選二手腕錶`;
  const description = watch.seoDescription ||
    `${watch.brand} ${watch.productName}${watch.price ? `，價格 NT$${watch.price.toLocaleString()}` : ''}。經過專業檢測認證的精品二手腕錶，品質保證。`;
  const image = watch.socialImage || watch.image || watch.thumbnail;

  return generateDynamicMetadata(title, description, image, `/pre-owned-watches-details/${watch.seoSlug || watch.id}`);
}

const WatchDetailPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const resolvedParams = await params;

  return (
    <Suspense fallback={<WatchDetailSkeleton />}>
      <WatchDetailContent slug={resolvedParams.slug} />
    </Suspense>
  );
};

export default WatchDetailPage;
