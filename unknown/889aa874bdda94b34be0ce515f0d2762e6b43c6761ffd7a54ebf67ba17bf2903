import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface Feature {
  id: number;
  title: string;
  description: string;
  image: string;
  alt: string;
}

interface FeatureGridProps {
  features: Feature[];
  title?: string;
  subtitle?: string;
}

const FeatureGrid = ({ features, title, subtitle }: FeatureGridProps) => {
  return (
    <section className="py-24 bg-gradient-to-br from-secondary/30 via-background to-secondary/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        {(title || subtitle) && (
          <div className="text-center mb-20">
            {title && (
              <h2 className="text-2xl md:text-4xl font-bold text-foreground mb-6 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-base md:text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12">
          {features.map((feature, index) => (
            <Card
              key={feature.id}
              className={cn(
                "group overflow-hidden border-border/50 hover:border-accent/50 bg-card/80 backdrop-blur-sm",
                "transition-all duration-500 transform hover:-translate-y-3",
                "hover:shadow-2xl hover:shadow-accent/15",
                "animate-in fade-in slide-in-from-bottom-4",
                // 響應式錯落效果：只在大螢幕上應用
                index === 1 && "lg:translate-y-8"
              )}
              style={{
                animationDelay: `${index * 200}ms`,
                animationFillMode: 'both'
              }}
            >
              {/* Feature Image */}
              <div className="relative h-72 overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.alt}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Floating number indicator */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-accent/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {feature.id}
                </div>
              </div>

              {/* Feature Content */}
              <CardContent className="p-8">
                <h3 className={cn(
                  "text-xl font-bold text-foreground mb-4",
                  "group-hover:text-accent transition-colors duration-300"
                )}>
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed text-base">
                  {feature.description}
                </p>

                {/* Decorative element */}
                <div className="mt-6 w-12 h-1 bg-gradient-to-r from-accent to-accent/50 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureGrid;
