"use client";

import { useEffect } from 'react';

export default function AppointmentPage() {
  useEffect(() => {
    // Tally 表單載入腳本
    const script = document.createElement('script');
    script.innerHTML = `
      var d=document,w="https://tally.so/widgets/embed.js",v=function(){"undefined"!=typeof Tally?Tally.loadEmbeds():d.querySelectorAll("iframe[data-tally-src]:not([src])").forEach((function(e){e.src=e.dataset.tallySrc}))};if("undefined"!=typeof Tally)v();else if(d.querySelector('script[src="'+w+'"]')==null){var s=d.createElement("script");s.src=w,s.onload=v,s.onerror=v,d.body.appendChild(s);}
    `;
    document.body.appendChild(script);

    return () => {
      // 清理腳本
      const existingScript = document.querySelector('script[src="https://tally.so/widgets/embed.js"]');
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#2b354d' }}>
              PANGEA 機械錶智慧收藏盒預約體驗
            </h1>
            <p className="text-lg" style={{ color: '#2b354d', opacity: 0.8 }}>
              填寫以下表單，我們將盡快與您聯繫安排體驗時間
            </p>
          </div>
        </div>
      </div>

      {/* Tally 表單 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          <iframe
            data-tally-src="https://tally.so/embed/w4jWM5?alignLeft=1&hideTitle=1&transparentBackground=1&dynamicHeight=1"
            loading="lazy"
            width="100%"
            height="2890"
            frameBorder="0"
            marginHeight={0}
            marginWidth={0}
            title="Pangea 機械錶智慧收藏盒預約體驗"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
