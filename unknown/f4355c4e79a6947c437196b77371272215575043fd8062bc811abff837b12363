"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface CarouselSlide {
  id: number;
  image: string;
  alt: string;
  title?: string;
  description?: string;
}

interface AutoCarouselProps {
  slides: CarouselSlide[];
  interval?: number; // in milliseconds, default 4000 (4 seconds)
  title?: string;
  subtitle?: string;
}

const AutoCarousel = ({
  slides,
  interval = 4000,
  title,
  subtitle
}: AutoCarouselProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  // Navigation functions
  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  }, [slides.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  }, [slides.length]);

  // Auto play functionality
  const startAutoPlay = useCallback(() => {
    if (slides.length <= 1) return;

    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
    }

    autoPlayRef.current = setInterval(() => {
      nextSlide();
    }, interval);
  }, [slides.length, interval, nextSlide]);

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  }, []);

  // Auto play effect
  useEffect(() => {
    if (isAutoPlaying) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }

    return () => stopAutoPlay();
  }, [isAutoPlaying, startAutoPlay, stopAutoPlay]);

  // Touch handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setIsAutoPlaying(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }

    // Resume auto play after a delay
    setTimeout(() => setIsAutoPlaying(true), 3000);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        prevSlide();
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 3000);
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        nextSlide();
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 3000);
      }
    };

    const carousel = carouselRef.current;
    if (carousel) {
      carousel.addEventListener('keydown', handleKeyDown);
      return () => carousel.removeEventListener('keydown', handleKeyDown);
    }
  }, [nextSlide, prevSlide]);

  if (slides.length === 0) return null;

  return (
    <section className="py-24 bg-gradient-to-br from-primary/95 via-primary to-primary/90 text-primary-foreground relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-accent/10 to-accent/5" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        {(title || subtitle) && (
          <div className="text-center mb-20">
            {title && (
              <h2 className="text-2xl md:text-4xl font-bold text-primary-foreground mb-6 bg-gradient-to-r from-primary-foreground to-primary-foreground/80 bg-clip-text text-transparent">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-base md:text-lg text-primary-foreground/90 max-w-4xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Carousel Container */}
        <div
          ref={carouselRef}
          className="relative h-[60vh] md:h-[70vh] overflow-hidden rounded-2xl border border-border/20 shadow-2xl focus:outline-none focus:ring-2 focus:ring-accent/50"
          tabIndex={0}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          {slides.map((slide, index) => (
            <div
              key={slide.id}
              className={cn(
                "absolute inset-0 transition-opacity duration-1000 ease-in-out",
                index === currentSlide ? "opacity-100" : "opacity-0"
              )}
            >
              <Image
                src={slide.image}
                alt={slide.alt}
                fill
                className="object-cover"
                priority={index === 0}
              />

              {/* Overlay for text content */}
              {(slide.title || slide.description) && (
                <div className="absolute inset-0 bg-gradient-to-t from-primary/80 via-primary/20 to-transparent flex items-end">
                  <div className="p-8 text-primary-foreground">
                    {slide.title && (
                      <h3 className="text-xl md:text-2xl font-bold mb-2">
                        {slide.title}
                      </h3>
                    )}
                    {slide.description && (
                      <p className="text-base md:text-lg opacity-90">
                        {slide.description}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* Slide Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                className={cn(
                  "w-3 h-3 rounded-full transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-accent/50",
                  index === currentSlide
                    ? "bg-accent scale-125 shadow-lg shadow-accent/50"
                    : "bg-primary-foreground/50 hover:bg-primary-foreground/75 hover:scale-110"
                )}
                onClick={() => {
                  goToSlide(index);
                  setIsAutoPlaying(false);
                  setTimeout(() => setIsAutoPlaying(true), 3000);
                }}
                aria-label={`前往第 ${index + 1} 張投影片`}
              />
            ))}
          </div>

          {/* Navigation Arrows (Hidden by default, shown on hover for accessibility) */}
          <button
            onClick={() => {
              prevSlide();
              setIsAutoPlaying(false);
              setTimeout(() => setIsAutoPlaying(true), 3000);
            }}
            className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-primary-foreground/20 hover:bg-primary-foreground/40 text-primary-foreground opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-accent/50 focus:opacity-100"
            aria-label="上一張投影片"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={() => {
              nextSlide();
              setIsAutoPlaying(false);
              setTimeout(() => setIsAutoPlaying(true), 3000);
            }}
            className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-primary-foreground/20 hover:bg-primary-foreground/40 text-primary-foreground opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-accent/50 focus:opacity-100"
            aria-label="下一張投影片"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default AutoCarousel;
