export default function WatchDetailSkeleton() {
  return (
    <div className="min-h-screen bg-white">
      {/* 返回按鈕骨架 */}
      <div className="border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-200 rounded mr-2 animate-pulse"></div>
            <div className="w-12 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* 主要內容骨架 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 圖片區域骨架 */}
          <div>
            {/* 主圖片骨架 */}
            <div className="mb-4">
              <div className="relative w-full h-96 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>

            {/* 縮圖列表骨架 */}
            <div className="grid grid-cols-4 gap-2">
              {[...Array(4)].map((_, index) => (
                <div
                  key={index}
                  className="relative h-20 bg-gray-200 rounded-lg animate-pulse"
                ></div>
              ))}
            </div>
          </div>

          {/* 資訊區域骨架 */}
          <div>
            <div className="lg:pl-8">
              {/* 品牌骨架 */}
              <div className="mb-3">
                <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* 產品名稱骨架 */}
              <div className="mb-4">
                <div className="w-48 h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* 價格骨架 */}
              <div className="mb-6">
                <div className="w-36 h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* 描述骨架 */}
              <div className="mb-6 space-y-2">
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className="w-full h-4 bg-gray-200 rounded animate-pulse"
                    style={{ width: `${Math.random() * 40 + 60}%` }}
                  ></div>
                ))}
              </div>

              {/* Tag 骨架 */}
              <div className="mb-8 flex flex-wrap gap-2">
                {[...Array(3)].map((_, index) => (
                  <div
                    key={index}
                    className="w-20 h-6 bg-gray-200 rounded-full animate-pulse"
                  ></div>
                ))}
              </div>

              {/* CTA 按鈕骨架 */}
              <div className="mb-8">
                <div className="w-full h-12 bg-gray-200 rounded-lg animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
