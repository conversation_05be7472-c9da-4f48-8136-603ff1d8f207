import { useState, useEffect, useCallback, useRef } from 'react';

interface UseLazyLoadingOptions<T> {
  fetchFunction: (page: number, pageSize: number) => Promise<{ items: T[]; total: number; hasMore: boolean }>;
  pageSize?: number;
  initialLoad?: boolean;
}

interface UseLazyLoadingReturn<T> {
  items: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => void;
  isLoadingMore: boolean;
}

export function useLazyLoading<T>({
  fetchFunction,
  pageSize = 12,
  initialLoad = true,
}: UseLazyLoadingOptions<T>): UseLazyLoadingReturn<T> {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(initialLoad);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const isInitialized = useRef(false);

  const loadData = useCallback(async (page: number, append: boolean = false) => {
    try {
      if (page === 0) {
        setLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      const result = await fetchFunction(page, pageSize);
      
      if (append) {
        setItems(prev => [...prev, ...result.items]);
      } else {
        setItems(result.items);
      }
      
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : '載入失敗');
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  }, [fetchFunction, pageSize]);

  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      loadData(currentPage + 1, true);
    }
  }, [loadData, currentPage, isLoadingMore, hasMore]);

  const refresh = useCallback(() => {
    setCurrentPage(0);
    setHasMore(true);
    loadData(0, false);
  }, [loadData]);

  useEffect(() => {
    if (initialLoad && !isInitialized.current) {
      isInitialized.current = true;
      loadData(0, false);
    }
  }, [initialLoad, loadData]);

  return {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isLoadingMore,
  };
}

// Hook for infinite scroll with Intersection Observer
export function useInfiniteScroll(
  loadMore: () => void,
  hasMore: boolean,
  isLoading: boolean
) {
  const [isFetching, setIsFetching] = useState(false);
  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!targetRef.current || !hasMore || isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading && !isFetching) {
          setIsFetching(true);
          loadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px',
      }
    );

    observer.observe(targetRef.current);

    return () => observer.disconnect();
  }, [loadMore, hasMore, isLoading, isFetching]);

  useEffect(() => {
    if (!isLoading) {
      setIsFetching(false);
    }
  }, [isLoading]);

  return { targetRef, isFetching };
}

// Hook for image lazy loading
export function useImageLazyLoading() {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const imageRefs = useRef<Map<string, HTMLImageElement>>(new Map());

  const registerImage = useCallback((src: string, imgElement: HTMLImageElement) => {
    imageRefs.current.set(src, imgElement);
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            if (src && !loadedImages.has(src)) {
              img.src = src;
              img.onload = () => {
                setLoadedImages(prev => new Set([...prev, src]));
                observer.unobserve(img);
              };
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    imageRefs.current.forEach((img) => {
      observer.observe(img);
    });

    return () => observer.disconnect();
  }, [loadedImages]);

  const isImageLoaded = useCallback((src: string) => {
    return loadedImages.has(src);
  }, [loadedImages]);

  return { registerImage, isImageLoaded };
}
